'use client';

import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { User } from '@/types/account';

// Generate the Amplify data client
const client = generateClient<Schema>();

const BASE_MEMBER_SELECTION_LIST = [
  'id',
  'firstName',
  'lastName',
  'email',
  'phoneNumber',
  'birthdate',
  'state',
  'cognitoId',
  'role',
  'subrole',
  'status',
  'journeyStatus',
  'createdAt',
  'linkedAccounts.*',
] as const;

// Profile selection list for user settings (excludes assignedWelonTrust relationship)
const PROFILE_SELECTION_LIST = [
  'id',
  'firstName',
  'lastName',
  'email',
  'phoneNumber',
  'birthdate',
  'state',
  'cognitoId',
  'role',
  'subrole',
  'status',
  'journeyStatus',
  'createdAt',
  'assignedWelonTrustId',
] as const;

const BASE_ADMIN_SELECTION_LIST = [
  'id',
  'firstName',
  'lastName',
  'email',
  'phoneNumber',
  'birthdate',
  'state',
  'cognitoId',
  'role',
  'subrole',
  'status',
  'journeyStatus',
  'createdAt',
  'linkedAccounts.*',
  'assignedWelonTrust.*',
] as const;

/**
 * Amplify data service for user management
 * Replaces the mock data with real Amplify operations
 */

export const handleChangeUserRole = async (
  newRole: string,
  userId: string,
  newSubrole?: string
) => {
  // Import roleSubroles for default subrole logic
  const { roleSubroles } = await import('@/lib/validations/user');

  // Map role names to the format expected by addUserToGroup function
  const roleMapping: Record<string, string> = {
    Member: 'member',
    Administrator: 'administrator',
    WelonTrust: 'welontrust',
    Professional: 'member', // Professional users are treated as members in Cognito
  };

  const mappedGroupName = roleMapping[newRole] || 'member';

  // Change role in Cognito groups
  const response = await client.mutations.addUserToGroup({
    groupName: mappedGroupName,
    userId,
  });

  let roleChangeSuccess = false;

  console.log('===> ROLE CHANGE RESPONSE:', response);

  try {
    if (typeof response.data === 'string') {
      try {
        const roleChangeParsedResponse = JSON.parse(response.data);

        // Force proper object access
        let parsedObj = roleChangeParsedResponse;

        // If it's somehow still a string, try parsing again
        if (typeof parsedObj === 'string') {
          parsedObj = JSON.parse(parsedObj);
        }

        // Direct property access with multiple checks
        roleChangeSuccess = parsedObj.success === true;
      } catch (parseError) {
        console.error('Error parsing role change response:', parseError);
        // Fallback: regex search for success
        const successMatch = response.data.match(/"success"\s*:\s*true/);
        roleChangeSuccess = !!successMatch;
      }
    } else {
      roleChangeSuccess = false;
    }
  } catch (error) {
    console.error('Error processing role change response:', error);
    roleChangeSuccess = false;
  }

  console.log('===> ROLE CHANGE SUCCESS:', roleChangeSuccess);

  // If role change was successful, always update both role and subrole in database
  if (roleChangeSuccess) {
    try {
      const updateData: any = {
        id: userId,
        role: newRole as
          | 'Member'
          | 'Administrator'
          | 'WelonTrust'
          | 'Professional',
      };

      // Handle subrole logic
      let finalSubrole = null;
      if (newSubrole && newSubrole.trim() !== '') {
        // Use provided subrole if it's not empty
        finalSubrole = newSubrole;
      } else {
        // If no subrole provided or empty, set default subrole for the role
        const availableSubroles =
          roleSubroles[newRole as keyof typeof roleSubroles] || [];
        finalSubrole =
          availableSubroles.length > 0 ? availableSubroles[0] : null;
      }

      updateData.subrole = finalSubrole;

      console.log('===> UPDATING USER WITH DATA:', updateData);

      const { data: updatedUser, errors } =
        await client.models.User.update(updateData);

      if (errors) {
        console.error('Errors updating user role and subrole:', errors);
        return { success: false };
      }

      console.log('===> USER ROLE AND SUBROLE UPDATED SUCCESSFULLY:', {
        id: updatedUser?.id,
        role: updatedUser?.role,
        subrole: updatedUser?.subrole,
      });
    } catch (error) {
      console.error('Error updating user role and subrole in database:', error);
      return { success: false };
    }
  }

  return {
    success: roleChangeSuccess,
  };
};

export const handleActivateDeactivateUser = async (
  userId: string,
  activate: boolean
) => {
  const response = await client.mutations.activateDeactivateUser({
    userId,
    activate,
  });

  if (typeof response.data === 'string') {
    const parsedResponse = JSON.parse(response.data);
    return {
      success: parsedResponse.success,
      message: parsedResponse.message,
      data: parsedResponse.data,
    };
  } else {
    return { success: false };
  }
};

// Transform Amplify User model to frontend User type
function transformAmplifyUser(amplifyUser: any): User {
  console.log(
    'Transforming user:',
    amplifyUser.id,
    'linkedAccounts type:',
    typeof amplifyUser.linkedAccounts
  );

  console.log('===> AMPLIFY USER', amplifyUser);

  // Handle linkedAccounts - it might be an array, a lazy-loaded function, or undefined
  let linkedAccountsArray: any[] = [];

  if (Array.isArray(amplifyUser.linkedAccounts)) {
    linkedAccountsArray = amplifyUser.linkedAccounts;
  } else if (
    amplifyUser.linkedAccounts &&
    typeof amplifyUser.linkedAccounts === 'object'
  ) {
    // If it's an object with data property (common in Amplify responses)
    linkedAccountsArray = amplifyUser.linkedAccounts.data || [];
  } else if (typeof amplifyUser.linkedAccounts === 'function') {
    // If it's a lazy-loaded function, we can't resolve it here
    // In a real app, you might want to call the function to load the data
    console.log('linkedAccounts is a function - skipping for now');
    linkedAccountsArray = [];
  } else {
    // If it's undefined or null, default to empty array
    linkedAccountsArray = [];
  }

  return {
    id: amplifyUser.id,
    firstName: amplifyUser.firstName,
    lastName: amplifyUser.lastName,
    name: `${amplifyUser.firstName} ${amplifyUser.lastName}`,
    email: amplifyUser.email,
    phoneNumber: amplifyUser.phoneNumber,
    birthdate: amplifyUser.birthdate,
    state: amplifyUser.state,
    role: amplifyUser.role,
    subrole: amplifyUser.subrole,
    status: amplifyUser.status,
    journeyStatus: amplifyUser.journeyStatus,
    createdAt: amplifyUser.createdAt,
    cognitoId: amplifyUser.cognitoId,
    linkedAccounts: linkedAccountsArray.map((account: any) => ({
      id: account.id,
      userId: account.userId,
      linkedUserId: account.linkedUserId,
      linkType: account.linkType,
      status: account.status,
      permissions: account.permissions || [],
      createdAt: account.createdAt,
      updatedAt: account.updatedAt,
      expiresAt: account.expiresAt,
    })),
    assignedWelonTrust: amplifyUser.assignedWelonTrust
      ? {
          id: amplifyUser.assignedWelonTrust.id,
          welonTrustUserId: amplifyUser.assignedWelonTrust.welonTrustUserId,
          welonTrustName: amplifyUser.assignedWelonTrust.welonTrustName,
          welonTrustEmail: amplifyUser.assignedWelonTrust.welonTrustEmail,
          assignedAt: amplifyUser.assignedWelonTrust.assignedAt,
          assignedBy: amplifyUser.assignedWelonTrust.assignedBy,
          status: amplifyUser.assignedWelonTrust.status,
        }
      : undefined,
    assignedWelonTrustId: amplifyUser.assignedWelonTrustId,
  };
}

// Transform Amplify User model to frontend User type (profile version - no relationships)
function transformAmplifyUserProfile(amplifyUser: any): User {
  console.log('===> AMPLIFY USER PROFILE', amplifyUser);

  return {
    id: amplifyUser.id,
    firstName: amplifyUser.firstName,
    lastName: amplifyUser.lastName,
    name: `${amplifyUser.firstName} ${amplifyUser.lastName}`,
    email: amplifyUser.email,
    phoneNumber: amplifyUser.phoneNumber,
    birthdate: amplifyUser.birthdate,
    state: amplifyUser.state,
    role: amplifyUser.role,
    subrole: amplifyUser.subrole,
    status: amplifyUser.status,
    journeyStatus: amplifyUser.journeyStatus,
    createdAt: amplifyUser.createdAt,
    cognitoId: amplifyUser.cognitoId,
    linkedAccounts: [], // Empty for profile queries
    assignedWelonTrust: undefined, // Not included in profile queries
    assignedWelonTrustId: amplifyUser.assignedWelonTrustId,
  };
}

// Fetch all users with their relationships
export async function fetchUsers(): Promise<User[]> {
  try {
    // @ts-ignore
    const { data: users, errors } = await client.models.User.list({
      filter: {
        cognitoId: {
          ne: undefined,
        },
      },
      selectionSet: BASE_ADMIN_SELECTION_LIST,
    });

    if (errors) {
      console.error('Errors fetching users:', errors);
      throw new Error('Failed to fetch users from database');
    }

    return users.map(transformAmplifyUser);
  } catch (error) {
    console.error('Error fetching users:', error);
    throw new Error('Failed to fetch users from database');
  }
}

// Fetch a single user by ID with relationships
export async function fetchUserById(userId: string): Promise<User | null> {
  try {
    const { data: user, errors } = await client.models.User.get(
      {
        id: userId,
      },
      {
        selectionSet: BASE_ADMIN_SELECTION_LIST,
      }
    );

    if (errors) {
      console.error('Errors fetching user:', errors);
      throw new Error('Failed to fetch user from database');
    }

    if (!user) {
      return null;
    }

    console.log('Raw user data from Amplify:', user); // Debug log
    return transformAmplifyUser(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    throw new Error('Failed to fetch user from database');
  }
}

export const getUserByIdWithWelonTrustUsers = async (
  userId: string
): Promise<{ user: User | null; welonTrustUsers: User[] }> => {
  const [fetchedUser, welonTrustUsersData] = await Promise.all([
    fetchUserById(userId),
    fetchWelonTrustUsers(),
  ]);

  return {
    user: fetchedUser,
    welonTrustUsers: welonTrustUsersData,
  };
};

// Update a user (admin version with full data access)
export async function updateUser(
  userId: string,
  updates: Partial<User>
): Promise<User> {
  try {
    // Handle status changes through Cognito activation/deactivation
    if (
      updates.status !== undefined &&
      (updates.status === 'active' || updates.status === 'inactive')
    ) {
      const activate = updates.status === 'active';
      const result = await handleActivateDeactivateUser(userId, activate);

      if (!result.success) {
        throw new Error(
          `Failed to ${activate ? 'activate' : 'deactivate'} user in Cognito`
        );
      }

      // The lambda function already updates the database status, so we need to fetch the updated user
      const { data: users } = await client.models.User.list({
        filter: { id: { eq: userId } },
        selectionSet: BASE_ADMIN_SELECTION_LIST,
      });

      if (!users || users.length === 0) {
        throw new Error('User not found after status update');
      }

      return transformAmplifyUser(users[0]);
    }

    // Extract only the fields that can be updated directly on the User model
    const userUpdates: any = {};

    if (updates.name !== undefined) {
      // Split the full name into first and last name
      const nameParts = updates.name.trim().split(' ');
      userUpdates.firstName = nameParts[0] || '';
      userUpdates.lastName = nameParts.slice(1).join(' ') || '';
    }
    if (updates.firstName !== undefined)
      userUpdates.firstName = updates.firstName;
    if (updates.lastName !== undefined) userUpdates.lastName = updates.lastName;
    if (updates.email !== undefined) userUpdates.email = updates.email;
    if (updates.subrole !== undefined) userUpdates.subrole = updates.subrole;
    if (updates.assignedWelonTrustId !== undefined)
      userUpdates.assignedWelonTrustId = updates.assignedWelonTrustId;
    if (updates.journeyStatus !== undefined)
      userUpdates.journeyStatus = updates.journeyStatus;

    // Add support for additional profile fields
    if ((updates as any).phoneNumber !== undefined)
      userUpdates.phoneNumber = (updates as any).phoneNumber;
    if ((updates as any).birthdate !== undefined)
      userUpdates.birthdate = (updates as any).birthdate;
    if ((updates as any).state !== undefined)
      userUpdates.state = (updates as any).state;

    const { data: updatedUser, errors } = await client.models.User.update({
      id: userId,
      ...userUpdates,
    });

    if (errors) {
      console.error('Errors updating user:', errors);
      throw new Error('Failed to update user');
    }

    if (!updatedUser) {
      throw new Error('User not found');
    }

    // Fetch the updated user with relationships to return complete data
    const completeUser = await fetchUserById(userId);
    if (!completeUser) {
      throw new Error('Failed to fetch updated user');
    }

    return completeUser;
  } catch (error) {
    console.error('Error updating user:', error);
    throw new Error('Failed to update user');
  }
}

// Update user profile (member version without restricted relationships)
export async function updateUserProfile(
  userId: string,
  updates: Partial<User>
): Promise<User> {
  try {
    // Extract only the fields that can be updated directly on the User model
    const userUpdates: any = {};

    if (updates.name !== undefined) {
      // Split the full name into first and last name
      const nameParts = updates.name.trim().split(' ');
      userUpdates.firstName = nameParts[0] || '';
      userUpdates.lastName = nameParts.slice(1).join(' ') || '';
    }
    if (updates.firstName !== undefined)
      userUpdates.firstName = updates.firstName;
    if (updates.lastName !== undefined) userUpdates.lastName = updates.lastName;
    if (updates.email !== undefined) userUpdates.email = updates.email;

    // Add support for additional profile fields
    if ((updates as any).phoneNumber !== undefined)
      userUpdates.phoneNumber = (updates as any).phoneNumber;
    if ((updates as any).birthdate !== undefined)
      userUpdates.birthdate = (updates as any).birthdate;
    if ((updates as any).state !== undefined)
      userUpdates.state = (updates as any).state;

    const { data: updatedUser, errors } = await client.models.User.update({
      id: userId,
      ...userUpdates,
    });

    if (errors) {
      console.error('Errors updating user profile:', errors);
      throw new Error('Failed to update user profile');
    }

    if (!updatedUser) {
      throw new Error('User not found');
    }

    // Return the updated user data directly (transformed from the update response)
    return transformAmplifyUserProfile(updatedUser);
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw new Error('Failed to update user profile');
  }
}

// Delete a user
export async function deleteUser(userId: string): Promise<void> {
  try {
    const { errors } = await client.models.User.delete({
      id: userId,
    });

    if (errors) {
      console.error('Errors deleting user:', errors);
      throw new Error('Failed to delete user');
    }
  } catch (error) {
    console.error('Error deleting user:', error);
    throw new Error('Failed to delete user');
  }
}

// Create a new user
export async function createUser(
  userData: Omit<User, 'id' | 'createdAt'>
): Promise<User> {
  try {
    // Split the full name into first and last name
    const nameParts = userData.name.trim().split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    const { data: newUser, errors } = await client.models.User.create({
      firstName,
      lastName,
      email: userData.email,
      role: userData.role,
      phoneNumber: '123456',
      birthdate: '1990-01-01',
      cognitoId: '123456',
      state: 'CA',
      subrole: userData.subrole,
      status: userData.status,
      journeyStatus: userData.journeyStatus,
      createdAt: new Date().toISOString(),
    });

    if (errors) {
      console.error('Errors creating user:', errors);
      throw new Error('Failed to create user');
    }

    if (!newUser) {
      throw new Error('Failed to create user');
    }

    return transformAmplifyUser(newUser);
  } catch (error) {
    console.error('Error creating user:', error);
    throw new Error('Failed to create user');
  }
}

// Fetch a single user by Cognito ID
export async function fetchUserByCognitoId(
  cognitoId: string
): Promise<User | null> {
  try {
    const { data: users, errors } = await client.models.User.list({
      filter: {
        cognitoId: {
          eq: cognitoId,
        },
      },
      selectionSet: BASE_MEMBER_SELECTION_LIST,
    });

    if (errors) {
      console.error('Errors fetching user by Cognito ID:', errors);
      throw new Error('Failed to fetch user from database');
    }

    if (!users || users.length === 0) {
      return null;
    }

    console.log('===> USER', users[0]);

    return transformAmplifyUser(users[0]);
  } catch (error) {
    console.error('Error fetching user by Cognito ID:', error);
    throw new Error('Failed to fetch user from database');
  }
}

// Fetch user profile data for settings (excludes assignedWelonTrust to avoid permission issues)
export async function fetchUserProfileByCognitoId(
  cognitoId: string
): Promise<User | null> {
  try {
    const { data: users, errors } = await client.models.User.list({
      filter: {
        cognitoId: {
          eq: cognitoId,
        },
      },
      selectionSet: PROFILE_SELECTION_LIST,
    });

    if (errors) {
      console.error('Errors fetching user profile by Cognito ID:', errors);
      throw new Error('Failed to fetch user profile from database');
    }

    if (!users || users.length === 0) {
      return null;
    }

    console.log('===> USER PROFILE', users[0]);

    return transformAmplifyUserProfile(users[0]);
  } catch (error) {
    console.error('Error fetching user profile by Cognito ID:', error);
    throw new Error('Failed to fetch user profile from database');
  }
}

// Fetch users assigned to a specific Welon Trust user
export async function fetchUsersAssignedToWelonTrust(): Promise<User[]> {
  try {
    const { data: users, errors: userErrors } = await client.models.User.list({
      filter: { role: { eq: 'Member' } },
      selectionSet: BASE_ADMIN_SELECTION_LIST,
    });

    console.log('===> ASSIGNED USERS LIST', users);

    if (userErrors) {
      console.error('Errors fetching assigned users:', userErrors);
      throw new Error('Failed to fetch assigned users from database');
    }

    return users.map(transformAmplifyUser);
  } catch (error) {
    console.error('Error fetching users assigned to Welon Trust:', error);
    throw new Error(
      'Failed to fetch users assigned to Welon Trust from database'
    );
  }
}

// Fetch Welon Trust users for assignment dropdown
export async function fetchWelonTrustUsers(): Promise<User[]> {
  try {
    const { data: users, errors } = await client.models.User.list({
      filter: {
        role: {
          eq: 'WelonTrust',
        },
      },
    });

    if (errors) {
      console.error('Errors fetching Welon Trust users:', errors);
      throw new Error('Failed to fetch Welon Trust users from database');
    }

    return users.map(transformAmplifyUser);
  } catch (error) {
    console.error('Error fetching Welon Trust users:', error);
    throw new Error('Failed to fetch Welon Trust users from database');
  }
}

// Fetch Welon Trust users by state with fallback to any available
export async function fetchWelonTrustUsersByState(
  state: string
): Promise<User[]> {
  try {
    // First, try to find Welon Trust users in the same state
    const { data: stateUsers, errors: stateErrors } =
      await client.models.User.list({
        filter: {
          and: [
            { role: { eq: 'WelonTrust' } },
            { state: { eq: state } },
            { status: { eq: 'active' } },
          ],
        },
      });

    if (stateErrors) {
      console.error('Errors fetching Welon Trust users by state:', stateErrors);
    }

    // If we found users in the same state, return them
    if (stateUsers && stateUsers.length > 0) {
      console.log(
        `Found ${stateUsers.length} Welon Trust users in state: ${state}`
      );
      return stateUsers.map(transformAmplifyUser);
    }

    // Fallback: fetch any available active Welon Trust users
    console.log(
      `No Welon Trust users found in state ${state}, fetching any available`
    );
    const { data: allUsers, errors: allErrors } = await client.models.User.list(
      {
        filter: {
          and: [{ role: { eq: 'WelonTrust' } }, { status: { eq: 'active' } }],
        },
      }
    );

    if (allErrors) {
      console.error('Errors fetching all Welon Trust users:', allErrors);
      throw new Error('Failed to fetch Welon Trust users from database');
    }

    if (!allUsers || allUsers.length === 0) {
      console.warn('No active Welon Trust users found in the system');
      return [];
    }

    console.log(
      `Found ${allUsers.length} active Welon Trust users as fallback`
    );
    return allUsers.map(transformAmplifyUser);
  } catch (error) {
    console.error('Error fetching Welon Trust users by state:', error);
    throw new Error('Failed to fetch Welon Trust users by state from database');
  }
}

// Create a linked account
export async function createLinkedAccount(
  userId: string,
  linkedAccountData: {
    linkedUserId: string;
    linkType: 'primary' | 'secondary' | 'delegate' | 'emergency';
    permissions: string[];
    expiresAt?: string;
  }
): Promise<void> {
  try {
    const { errors } = await client.models.LinkedAccount.create({
      userId,
      linkedUserId: linkedAccountData.linkedUserId,
      linkType: linkedAccountData.linkType,
      status: 'pending',
      permissions: linkedAccountData.permissions,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      expiresAt: linkedAccountData.expiresAt,
    });

    if (errors) {
      console.error('Errors creating linked account:', errors);
      throw new Error('Failed to create linked account');
    }
  } catch (error) {
    console.error('Error creating linked account:', error);
    throw new Error('Failed to create linked account');
  }
}

export const getAssignedToUserWelonTrustCognitoId = async (
  userId: string
): Promise<string | null> => {
  const { data: user } = await client.models.User.get(
    { id: userId },
    {
      selectionSet: ['assignedWelonTrustId'],
    }
  );

  return user?.assignedWelonTrustId || null;
};
