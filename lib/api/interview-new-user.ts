import { generateClient } from 'aws-amplify/data';
import { getCurrentUser } from 'aws-amplify/auth';
import type { Schema } from '@/amplify/data/resource';

// Generate the Amplify data client
const client = generateClient<Schema>();

// Types for the new interview models
export interface InterviewQuestion {
  questionId: string;
  text: string;
  type: 'text' | 'radio' | 'select' | 'checkbox';
  options?: string[];
  order: number;
  conditionalLogic?: Array<{
    conditionType: 'equals' | 'notEquals' | 'contains';
    expectedValue: string;
    showQuestionId: string;
  }>;
  questionValidation?: string;
  defaultNextQuestionId?: string;
  isHeadQuestion?: boolean;
}

export interface InterviewVersionWithQuestions {
  id: string;
  versionNumber: number;
  isActive: boolean;
  questions: InterviewQuestion[];
  interviewId: string;
}

export interface InterviewWithVersion {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  latestVersion: InterviewVersionWithQuestions;
}

export interface UserAnswer {
  questionId: string;
  answer: string;
  answeredAt: string;
}

export interface UserInterviewProgress {
  interviewVersionId: string;
  isCompleted: boolean;
  startedAt: string;
  completedAt?: string;
  currentQuestionId?: string;
  answers: UserAnswer[];
}

/**
 * Get all active interviews with their latest versions
 */
export const getActiveInterviews = async (): Promise<
  InterviewWithVersion[]
> => {
  try {
    const { data: interviews, errors } = await client.models.Interview.list({
      filter: { isActive: { eq: true } },
      selectionSet: [
        'id',
        'name',
        'description',
        'isActive',
        'createdAt',
        'updatedAt',
        'versions.id',
        'versions.versionNumber',
        'versions.isActive',
        'versions.questions.*',
      ],
    });

    if (errors) {
      console.error('Failed to fetch interviews:', errors);
      return [];
    }

    // Transform and get latest version for each interview
    const interviewsWithLatestVersion: InterviewWithVersion[] = [];

    for (const interview of interviews) {
      if (!interview.versions || interview.versions.length === 0) {
        continue;
      }

      // Find the latest active version
      const activeVersions = interview.versions.filter(v => v.isActive);
      if (activeVersions.length === 0) {
        continue;
      }

      const latestVersion = activeVersions.reduce((latest, current) =>
        current.versionNumber > latest.versionNumber ? current : latest
      );

      // Sort questions by order and transform to match InterviewQuestion interface
      const sortedQuestions: InterviewQuestion[] = (
        latestVersion.questions || []
      )
        .filter(
          (q): q is NonNullable<typeof q> => q !== null && q.type !== null
        )
        .map(q => ({
          questionId: q.questionId,
          text: q.text,
          type: q.type as 'text' | 'radio' | 'select' | 'checkbox',
          options:
            q.options?.filter((opt): opt is string => opt !== null) ||
            undefined,
          order: q.order,
          conditionalLogic:
            q.conditionalLogic
              ?.filter(
                (cl): cl is NonNullable<typeof cl> =>
                  cl !== null && cl.conditionType !== null
              )
              ?.map(cl => ({
                conditionType: cl.conditionType as
                  | 'equals'
                  | 'notEquals'
                  | 'contains',
                expectedValue: cl.expectedValue,
                showQuestionId: cl.showQuestionId,
              })) || undefined,
          defaultNextQuestionId: q.defaultNextQuestionId || undefined,
          isHeadQuestion: q.isHeadQuestion ?? undefined,
        }))
        .sort((a, b) => a.order - b.order);

      interviewsWithLatestVersion.push({
        id: interview.id,
        name: interview.name,
        description: interview.description || undefined,
        isActive: interview.isActive ?? true,
        latestVersion: {
          id: latestVersion.id,
          versionNumber: latestVersion.versionNumber,
          isActive: latestVersion.isActive ?? true,
          questions: sortedQuestions,
          interviewId: interview.id,
        },
      });
    }

    return interviewsWithLatestVersion;
  } catch (error) {
    console.error('Error fetching active interviews:', error);
    return [];
  }
};

/**
 * Get a specific interview with its latest version
 */
export const getInterviewById = async (
  interviewId: string
): Promise<InterviewWithVersion | null> => {
  try {
    const { data: interview, errors } = await client.models.Interview.get(
      { id: interviewId },
      {
        selectionSet: [
          'id',
          'name',
          'description',
          'isActive',
          'versions.id',
          'versions.versionNumber',
          'versions.isActive',
          'versions.questions.*',
        ],
      }
    );

    if (errors || !interview) {
      console.error('Failed to fetch interview:', errors);
      return null;
    }

    if (!interview.versions || interview.versions.length === 0) {
      return null;
    }

    // Find the latest active version
    const activeVersions = interview.versions.filter(v => v.isActive);
    if (activeVersions.length === 0) {
      return null;
    }

    const latestVersion = activeVersions.reduce((latest, current) =>
      current.versionNumber > latest.versionNumber ? current : latest
    );

    // Sort questions by order and transform to match InterviewQuestion interface
    const sortedQuestions: InterviewQuestion[] = (latestVersion.questions || [])
      .filter((q): q is NonNullable<typeof q> => q !== null && q.type !== null)
      .map(q => ({
        questionId: q.questionId,
        text: q.text,
        type: q.type as 'text' | 'radio' | 'select' | 'checkbox',
        options:
          q.options?.filter((opt): opt is string => opt !== null) || undefined,
        order: q.order,
        questionValidation: q.questionValidation || undefined,
        conditionalLogic:
          q.conditionalLogic
            ?.filter(
              (cl): cl is NonNullable<typeof cl> =>
                cl !== null && cl.conditionType !== null
            )
            ?.map(cl => ({
              conditionType: cl.conditionType as
                | 'equals'
                | 'notEquals'
                | 'contains',
              expectedValue: cl.expectedValue,
              showQuestionId: cl.showQuestionId,
            })) || undefined,
        defaultNextQuestionId: q.defaultNextQuestionId || undefined,
        isHeadQuestion: q.isHeadQuestion ?? undefined,
      }))
      .sort((a, b) => a.order - b.order);

    return {
      id: interview.id,
      name: interview.name,
      description: interview.description || undefined,
      isActive: interview.isActive ?? true,
      latestVersion: {
        id: latestVersion.id,
        versionNumber: latestVersion.versionNumber,
        isActive: latestVersion.isActive ?? true,
        questions: sortedQuestions,
        interviewId: interview.id,
      },
    };
  } catch (error) {
    console.error('Error fetching interview by ID:', error);
    return null;
  }
};

/**
 * Get user's interview progress for a specific interview version
 */
export const getUserInterviewProgress = async (
  interviewVersionId: string
): Promise<UserInterviewProgress | null> => {
  try {
    const user = await getCurrentUser();

    // Get current user data - try to find user by cognitoId
    const { data: userList, errors } = await client.models.User.list({
      filter: { cognitoId: { eq: user.userId } },
      selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
    });

    if (errors) {
      console.error('Failed to fetch user data:', errors);
      return null;
    }

    const userData = userList?.[0];
    if (!userData) {
      console.error('User not found with cognitoId:', user.userId);
      return null;
    }

    // Find progress for the specific interview version
    const progress = userData.interviewProgress?.find(
      p => p !== null && p.interviewVersionId === interviewVersionId
    );

    if (!progress) return null;

    // Transform to match UserInterviewProgress interface
    const transformedProgress: UserInterviewProgress = {
      interviewVersionId: progress.interviewVersionId,
      isCompleted: progress.isCompleted,
      startedAt: progress.startedAt,
      completedAt: progress.completedAt || undefined,
      currentQuestionId: progress.currentQuestionId || undefined,
      answers: (progress.answers || [])
        .filter(
          (a): a is NonNullable<typeof a> => a !== null && a.answer !== null
        )
        .map(a => ({
          questionId: a.questionId,
          answer: a.answer as string,
          answeredAt: a.answeredAt,
        })),
    };

    return transformedProgress;
  } catch (error) {
    console.error('Error fetching user interview progress:', error);
    return null;
  }
};

/**
 * Save or update user's answer to a question
 */
export const saveUserAnswer = async (
  interviewVersionId: string,
  questionId: string,
  answer: string,
  isCompleted: boolean = false
): Promise<boolean> => {
  try {
    const user = await getCurrentUser();
    console.log('Current user:', user.userId);

    // Get current user data - try to find user by cognitoId
    const { data: userList, errors: listErrors } =
      await client.models.User.list({
        filter: { cognitoId: { eq: user.userId } },
        selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
      });

    if (listErrors) {
      console.error('Failed to fetch user data:', listErrors);
      return false;
    }

    const userData = userList?.[0];
    if (!userData) {
      console.error('User not found with cognitoId:', user.userId);
      return false;
    }

    console.log('Found user:', userData.id, userData.cognitoId);

    const currentProgress = userData.interviewProgress || [];
    const existingProgressIndex = currentProgress.findIndex(
      p => p !== null && p.interviewVersionId === interviewVersionId
    );

    const now = new Date().toISOString();
    const newAnswer: UserAnswer = {
      questionId,
      answer,
      answeredAt: now,
    };

    let updatedProgress: UserInterviewProgress[];

    if (existingProgressIndex >= 0) {
      // Update existing progress
      const existing = currentProgress[existingProgressIndex];
      if (!existing) {
        console.error('Existing progress is null');
        return false;
      }
      const existingAnswers = existing.answers || [];

      // Remove any existing answer for this question and add the new one
      const filteredAnswers = existingAnswers
        .filter(
          (a): a is NonNullable<typeof a> =>
            a !== null && a.questionId !== questionId && a.answer !== null
        )
        .map(a => ({
          questionId: a.questionId,
          answer: a.answer as string,
          answeredAt: a.answeredAt,
        }));
      const updatedAnswers = [...filteredAnswers, newAnswer];

      // Transform and filter the progress data
      updatedProgress = currentProgress
        .filter((p): p is NonNullable<typeof p> => p !== null)
        .map(p => ({
          interviewVersionId: p.interviewVersionId,
          isCompleted: p.isCompleted,
          startedAt: p.startedAt,
          completedAt: p.completedAt || undefined,
          currentQuestionId: p.currentQuestionId || undefined,
          answers: (p.answers || [])
            .filter(
              (a): a is NonNullable<typeof a> => a !== null && a.answer !== null
            )
            .map(a => ({
              questionId: a.questionId,
              answer: a.answer as string,
              answeredAt: a.answeredAt,
            })),
        }));

      updatedProgress[existingProgressIndex] = {
        interviewVersionId: existing.interviewVersionId,
        isCompleted,
        startedAt: existing.startedAt,
        completedAt: isCompleted ? now : existing.completedAt || undefined,
        currentQuestionId: questionId,
        answers: updatedAnswers,
      };
    } else {
      // Create new progress entry
      const newProgress: UserInterviewProgress = {
        interviewVersionId,
        isCompleted,
        startedAt: now,
        completedAt: isCompleted ? now : undefined,
        currentQuestionId: questionId,
        answers: [newAnswer],
      };

      // Transform and filter the progress data, then add new progress
      const transformedProgress = currentProgress
        .filter((p): p is NonNullable<typeof p> => p !== null)
        .map(p => ({
          interviewVersionId: p.interviewVersionId,
          isCompleted: p.isCompleted,
          startedAt: p.startedAt,
          completedAt: p.completedAt || undefined,
          currentQuestionId: p.currentQuestionId || undefined,
          answers: (p.answers || [])
            .filter(
              (a): a is NonNullable<typeof a> => a !== null && a.answer !== null
            )
            .map(a => ({
              questionId: a.questionId,
              answer: a.answer as string,
              answeredAt: a.answeredAt,
            })),
        }));

      updatedProgress = [...transformedProgress, newProgress];
    }

    // Update user with new progress - use the user's id as primary key
    console.log('Updating user with ID:', userData.id);
    console.log('Progress data:', JSON.stringify(updatedProgress, null, 2));

    const { data: updatedUser, errors: updateErrors } =
      await client.models.User.update({
        id: userData.id, // Use the actual user ID from the fetched data
        interviewProgress: updatedProgress,
      });

    if (updateErrors) {
      console.error('Failed to update user interview progress:', updateErrors);
      return false;
    }

    if (!updatedUser) {
      console.error('No updated user data returned');
      return false;
    }

    console.log('Successfully updated user interview progress');

    return true;
  } catch (error) {
    console.error('Error saving user answer:', error);
    return false;
  }
};

/**
 * Reset user's progress for a specific interview version
 */
export const resetUserInterviewProgress = async (
  interviewVersionId: string
): Promise<boolean> => {
  try {
    const user = await getCurrentUser();

    // Get current user data - try to find user by cognitoId
    const { data: userList, errors: fetchErrors } =
      await client.models.User.list({
        filter: { cognitoId: { eq: user.userId } },
        selectionSet: ['id', 'cognitoId', 'interviewProgress.*'],
      });

    if (fetchErrors) {
      console.error('Failed to fetch user data:', fetchErrors);
      return false;
    }

    const userData = userList?.[0];
    if (!userData) {
      console.error('User not found with cognitoId:', user.userId);
      return false;
    }

    // Remove progress for the specific interview version
    const updatedProgress = (userData.interviewProgress || []).filter(
      p => p !== null && p.interviewVersionId !== interviewVersionId
    );

    // Update user with filtered progress - use the user's id as primary key
    const { data: updatedUser, errors: updateErrors } =
      await client.models.User.update({
        id: userData.id, // Use the actual user ID from the fetched data
        interviewProgress: updatedProgress,
      });

    if (updateErrors || !updatedUser) {
      console.error('Failed to reset user interview progress:', updateErrors);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error resetting user interview progress:', error);
    return false;
  }
};
