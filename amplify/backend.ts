import { defineBackend } from '@aws-amplify/backend';
import { PolicyStatement, Effect } from 'aws-cdk-lib/aws-iam';
import { auth } from './auth/resource';
import { data } from './data/resource';
import { say<PERSON>ello } from './functions/say-hello/resource';
import { storage } from './storage/resource';
import { postSignUp } from './functions/postSignUpTrigger/resource';
import { addUserToGroup } from './functions/addUserToGroup/resource';
import { checkEmail } from './functions/checkEmail/resource';
import { trackLoginAttempt } from './functions/trackLoginAttempt/resource';
import { generatePdf } from './functions/generatePdf/resource';
import { sendEmail } from './functions/sendEmail/resource';
import { generateVerificationToken } from './functions/generateVerificationToken/resource';
import { verifyEmailToken } from './functions/verifyEmailToken/resource';
import { activateDeactivateUser } from './functions/activateDeactivateUser/resource';

/**
 * @see https://docs.amplify.aws/react/build-a-backend/ to add storage, functions, and more
 */
const backend = defineBackend({
  sayHello,
  auth,
  data,
  addUserToGroup,
  checkEmail,
  storage,
  postSignUp,
  trackLoginAttempt,
  generatePdf,
  sendEmail,
  generateVerificationToken,
  verifyEmailToken,
  activateDeactivateUser,
});

// Add environment variables to the addUserToGroup function
backend.addUserToGroup.addEnvironment(
  'USER_POOL_ID',
  backend.auth.resources.userPool.userPoolId
);

// Add environment variables to the activateDeactivateUser function
backend.activateDeactivateUser.addEnvironment(
  'USER_POOL_ID',
  backend.auth.resources.userPool.userPoolId
);

// Add environment variables to the checkEmail function
backend.checkEmail.addEnvironment(
  'USER_POOL_ID',
  backend.auth.resources.userPool.userPoolId
);

backend.verifyEmailToken.addEnvironment(
  'USER_POOL_ID',
  backend.auth.resources.userPool.userPoolId
);

// Add SES permissions to the sendEmail function
backend.sendEmail.resources.lambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: [
      'ses:SendEmail',
      'ses:SendRawEmail',
      'ses:GetSendQuota',
      'ses:GetSendStatistics',
    ],
    resources: ['*'],
  })
);

// Grant the postConfirmation function access to the data resources
// backend.data.resources.graphqlApi.grantMutation(
//   backend.postConfirmation.resources.lambda,
//   'User'
// );
// backend.data.resources.graphqlApi.grantMutation(
//   backend.postConfirmation.resources.lambda,
//   'UserOnboarding'
// );

// extract L1 CfnUserPool resources
const { cfnUserPool, cfnUserPoolClient } = backend.auth.resources.cfnResources;
// modify cfnUserPool policies directly
cfnUserPool.policies = {
  passwordPolicy: {
    minimumLength: 12,
    requireLowercase: true,
    requireNumbers: true,
    requireSymbols: true,
    requireUppercase: true,
    temporaryPasswordValidityDays: 3,
  },
};

// Enable device tracking
cfnUserPool.deviceConfiguration = {
  challengeRequiredOnNewDevice: false,
  deviceOnlyRememberedOnUserPrompt: false,
};

if (cfnUserPoolClient) {
  cfnUserPoolClient.accessTokenValidity = 1;
  cfnUserPoolClient.idTokenValidity = 1;
  cfnUserPoolClient.refreshTokenValidity = 30;
  cfnUserPoolClient.tokenValidityUnits = {
    accessToken: 'hours',
    idToken: 'hours',
    refreshToken: 'days',
  };
}
