import { type ClientSchema, a, defineData } from '@aws-amplify/backend';

// Import all model definitions from schemas
import {
  Todo,
  UserOnboarding,
  LoginAttempt,
  LoginHistory,
  VerificationTokens,
  InterviewSet,
  InterviewQuestion,
  InterviewSetVersion,
  UserInterviewProgress,
  Template,
  TemplateVersion,
  EmergencyContact,
  LivingDocument,
  Document,
  DocumentUpdateLog,
  VaultAccess,
  User,
  WelonTrustAssignment,
  LinkedAccount,
  Attorney,
  EvidenceSubmission,
  DeadMansSwitch,
  CommunicationMethod,
  EscalationProtocol,
  DMSStatus,
  CheckInFrequency,
  CheckInHistory,
  EscalationEvent,
  DMSTestEvent,
  Interview,
  InterviewVersion,
  Question,
  ConditionalLogic,
  UserInterviewProgressNew,
  UserAnswer,
  Notification,
} from './schemas';
import { postSignUp } from '../functions/postSignUpTrigger/resource';
import { addUserToGroup } from '../functions/addUserToGroup/resource';
import { checkEmail } from '../functions/checkEmail/resource';
import { trackLoginAttempt } from '../functions/trackLoginAttempt/resource';
import { generatePdf } from '../functions/generatePdf/resource';
import { generateVerificationToken } from '../functions/generateVerificationToken/resource';
import { verifyEmailToken } from '../functions/verifyEmailToken/resource';
import { sendEmail } from '../functions/sendEmail/resource';
import { activateDeactivateUser } from '../functions/activateDeactivateUser/resource';

/**
 * You can use the secret SQL_CONNECTION_STRING to get access to the
 * DB for any code push. If you want to use a differencet user or non-default DB
 * then let me know. The default DB is call cfl_dev_db and the credentials will
 * be sent outside of the code.
 */

const schema = a
  .schema({
    // Demo/Example models
    Todo,

    // Authentication and user onboarding models
    UserOnboarding,
    LoginAttempt,
    LoginHistory,
    VerificationTokens,

    // Interview and questionnaire models
    InterviewSet,
    InterviewQuestion,
    UserInterviewProgress,
    InterviewSetVersion,

    // Template management models
    Template,
    TemplateVersion,

    // Emergency contact models
    EmergencyContact,

    // Living document models
    LivingDocument,
    DocumentUpdateLog,
    Document,

    // Vault access models
    VaultAccess,

    // User management models
    User,
    WelonTrustAssignment,
    LinkedAccount,
    UserInterviewProgressNew,
    UserAnswer,

    // Attorney management models
    Attorney,

    // Evidence submission models
    EvidenceSubmission,

    // Notification models
    Notification,

    Interview,
    InterviewVersion,
    Question,
    ConditionalLogic,

    addUserToGroup: a
      .mutation()
      .arguments({
        userId: a.string().required(),
        groupName: a.string().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(addUserToGroup))
      .returns(a.json()),

    checkEmail: a
      .query()
      .arguments({
        email: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(checkEmail))
      .returns(a.json()),

    trackLoginAttempt: a
      .mutation()
      .arguments({
        email: a.string().required(),
        success: a.boolean().required(),
        action: a.string().required(),
        ipAddress: a.string(),
        userAgent: a.string(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(trackLoginAttempt))
      .returns(a.json()),

    checkAccountLockout: a
      .query()
      .arguments({
        email: a.string().required(),
        action: a.string().required(),
      })
      .authorization(allow => [allow.guest()])
      .handler(a.handler.function(trackLoginAttempt))
      .returns(a.json()),

    generatePdf: a
      .query()
      .arguments({
        html: a.string().required(),
        options: a.json(),
        css: a.string(),
      })
      .authorization(allow => [allow.authenticated()])
      .handler(a.handler.function(generatePdf))
      .returns(a.json()),

    sendEmail: a
      .mutation()
      .arguments({
        to: a.string().required(),
        subject: a.string().required(),
        message: a.string().required(),
        verificationLink: a.string(),
        emailType: a.string(),
        isNewAccount: a.boolean(),
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(sendEmail))
      .returns(a.json()),

    generateVerificationToken: a
      .mutation()
      .arguments({
        email: a.string().required(),
        verificationType: a.string().required(), // 'accountConfirmation' or 'passwordReset'
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(generateVerificationToken))
      .returns(a.json()),

    verifyEmailToken: a
      .mutation()
      .arguments({
        email: a.string().required(),
        token: a.string().required(),
        verificationType: a.string().required(), // 'accountConfirmation' or 'passwordReset'
        newPassword: a.string(), // Required for passwordReset
      })
      .authorization(allow => [allow.guest(), allow.authenticated()])
      .handler(a.handler.function(verifyEmailToken))
      .returns(a.json()),

    activateDeactivateUser: a
      .mutation()
      .arguments({
        userId: a.string().required(),
        activate: a.boolean().required(),
      })
      .authorization(allow => [allow.group('ADMINS')])
      .handler(a.handler.function(activateDeactivateUser))
      .returns(a.json()),
    DeadMansSwitch,
    CheckInFrequency,
    CommunicationMethod,
    EscalationProtocol,
    DMSStatus,
    CheckInHistory,
    EscalationEvent,
    DMSTestEvent,
  })
  .authorization(allow => [
    allow.resource(postSignUp).to(['query', 'mutate']),
    allow.resource(addUserToGroup).to(['query', 'mutate']),
    allow.resource(checkEmail).to(['query']),
    allow.resource(trackLoginAttempt).to(['query', 'mutate']),
    allow.resource(generatePdf).to(['query']),
    allow.resource(sendEmail).to(['mutate']),
    allow.resource(generateVerificationToken).to(['query', 'mutate']),
    allow.resource(verifyEmailToken).to(['query', 'mutate']),
    allow.resource(activateDeactivateUser).to(['query', 'mutate']),
    allow.group('ADMINS').to(['list', 'get', 'create', 'update', 'delete']),
    allow.guest().to(['list', 'get', 'create', 'update', 'delete']),
  ]);

export type Schema = ClientSchema<typeof schema>;

export const data = defineData({
  schema,
  authorizationModes: {
    defaultAuthorizationMode: 'userPool',
    apiKeyAuthorizationMode: {
      expiresInDays: 30,
    },
  },
});

/*== STEP 2 ===============================================================
Go to your frontend source code. From your client-side code, generate a
Data client to make CRUDL requests to your table. (THIS SNIPPET WILL ONLY
WORK IN THE FRONTEND CODE FILE.)

Using JavaScript or Next.js React Server Components, Middleware, Server
Actions or Pages Router? Review how to generate Data clients for those use
cases: https://docs.amplify.aws/gen2/build-a-backend/data/connect-to-API/
=========================================================================*/

/*
"use client"
import { generateClient } from "aws-amplify/data";
import type { Schema } from "@/amplify/data/resource";

const client = generateClient<Schema>() // use this Data client for CRUDL requests
*/

/*== STEP 3 ===============================================================
Fetch records from the database and use them in your frontend component.
(THIS SNIPPET WILL ONLY WORK IN THE FRONTEND CODE FILE.)
=========================================================================*/

/* For example, in a React component, you can use this snippet in your
  function's RETURN statement */
// const { data: todos } = await client.models.Todo.list()

// return <ul>{todos.map(todo => <li key={todo.id}>{todo.content}</li>)}</ul>
