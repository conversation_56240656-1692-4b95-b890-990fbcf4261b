import type { Schema } from '../../data/resource';
import { sendEmail } from '../../shared/email-service';
import { withSentry, initSentry } from '../../shared/sentry-config';

const handlerFunction: Schema['sendEmail']['functionHandler'] = async event => {
  // Initialize Sentry at the start of the function
  initSentry();
  console.log('Email send request received:', JSON.stringify(event, null, 2));

  try {
    // Extract arguments from the event
    const { to, subject, message } = event.arguments;

    // Validate required fields
    if (!to || !subject || !message) {
      return {
        success: false,
        error: 'Missing required fields: to, subject, message',
      };
    }

    // Send email using shared service
    const result = await sendEmail({
      to,
      subject,
      message,
      templateType: 'branded',
    });

    if (result.success) {
      const response = {
        success: true,
        message: 'Email sent successfully via Amazon SES',
        emailId: result.messageId || `email_${Date.now()}`,
        timestamp: new Date().toISOString(),
        sender: '<EMAIL>',
      };

      console.log('Returning response:', response);
      return response;
    } else {
      const errorResponse = {
        success: false,
        error: result.error || 'Failed to send email',
        details: result.details || 'Unknown error',
      };

      console.log('Returning error response:', errorResponse);
      return errorResponse;
    }
  } catch (error) {
    console.error('Error sending email:', error);

    const errorResponse = {
      success: false,
      error: 'Failed to send email',
      details: error instanceof Error ? error.message : 'Unknown error',
    };

    console.log('Returning error response:', errorResponse);
    return errorResponse;
  }
};

// Export the handler wrapped with Sentry
export const handler = withSentry(handlerFunction);
