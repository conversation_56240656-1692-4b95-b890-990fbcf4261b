# Send Email Function

AWS Lambda function for sending emails via Amazon SES with support for multiple templates and comprehensive testing.

## Function Overview

This function handles email sending through AWS SES with the following features:

- **Input validation** for required fields (to, subject, message)
- **Template support** with branded HTML formatting
- **Error handling** with detailed error responses
- **Logging** for debugging and monitoring
- **Integration** with shared email service

## Testing

### Test Modes

The email function supports two testing modes:

1. **Mock Mode (Default)** - Uses mocked AWS SES for fast unit testing
2. **Real Mode** - Sends actual emails via AWS SES for integration testing

### Available Test Scripts

```bash
# Run all email-related tests
pnpm test:email

# Run unit tests with mocked email service
pnpm test:email:mock

# Run integration tests with mocked email service
pnpm test:email:integration

# Run integration tests with REAL email sending
pnpm test:email:real

# Run shared email service tests
pnpm test:email:service

# Run with custom email recipient
TEST_EMAIL=<EMAIL> pnpm test:email:real
```

### Test Coverage

#### Unit Tests (`handler.test.ts`)

- ✅ Input validation (missing fields)
- ✅ Successful email sending
- ✅ Error handling from email service
- ✅ Exception handling
- ✅ Logging behavior
- ✅ Response format validation

#### Integration Tests (`handler.integration.test.ts`)

- ✅ Basic email sending functionality
- ✅ HTML content handling
- ✅ Special characters and Unicode
- ✅ Template rendering (branded)
- ✅ Performance testing
- ✅ Concurrent email handling
- ✅ Real email verification (when in real mode)

#### Email Service Tests (`email-service.test.ts`)

- ✅ All template types (basic, branded, scheduled)
- ✅ HTML template generation
- ✅ Error handling
- ✅ Test email functions
- ✅ Scheduled email functions
- ✅ HTML escaping and formatting

### Running Real Email Tests

To test with actual email sending:

```bash
# Send test emails to default address (<EMAIL>)
TEST_MODE=real pnpm test:email:real

# Send test emails to custom address
TEST_MODE=real TEST_EMAIL=<EMAIL> pnpm test:email:real

# Run with verbose logging
TEST_MODE=real TEST_VERBOSE=true pnpm test:email:real
```

**⚠️ Prerequisites for Real Email Tests:**

- AWS credentials configured (AWS CLI, environment variables, or IAM role)
- Amazon SES configured with verified sender email
- Proper AWS region set (defaults to us-east-1)

### Test Email Examples

When running real email tests, you'll receive emails like:

1. **Basic Test Email** - Simple text with basic formatting
2. **HTML Content Test** - Rich HTML with styling and lists
3. **Special Characters Test** - Unicode, emojis, and symbols
4. **Branded Template Test** - Full Childfree Legacy branding
5. **Performance Test** - Speed and reliability verification
6. **Concurrent Test** - Multiple simultaneous emails
7. **Verification Email** - Final confirmation with test summary

## Function Interface

### Input Event Structure

```typescript
{
  arguments: {
    to: string;        // Required: recipient email
    subject: string;   // Required: email subject
    message: string;   // Required: email content
    from?: string;     // Optional: sender (uses default)
  }
}
```

### Response Structure

**Success Response:**

```typescript
{
  success: true;
  message: 'Email sent successfully via Amazon SES';
  emailId: string; // SES Message ID or generated ID
  timestamp: string; // ISO timestamp
  sender: string; // Sender email address
}
```

**Error Response:**

```typescript
{
  success: false;
  error: string;       // Error message
  details?: string;    // Additional error details
}
```

## Email Templates

The function uses the **branded template** by default, which includes:

- Childfree Legacy branding
- Professional HTML formatting
- Responsive design
- Footer with application information

## Error Handling

The function handles various error scenarios:

- Missing required fields
- AWS SES service errors
- Network connectivity issues
- Invalid email addresses
- Rate limiting

## Monitoring and Logging

All email operations are logged with:

- Incoming request details
- Email parameters
- Success/failure status
- Error details
- Response data

## Usage Examples

### GraphQL Query

```graphql
query SendEmail {
  sendEmail(
    to: "<EMAIL>"
    subject: "Test Email"
    message: "Hello from Childfree Legacy!"
  ) {
    success
    message
    emailId
    timestamp
    sender
  }
}
```

### Direct Function Invocation

```typescript
const result = await handler({
  arguments: {
    to: '<EMAIL>',
    subject: 'Test Subject',
    message: 'Test message content',
  },
});
```

## Development

### Adding New Tests

1. Add unit tests to `__tests__/handler.test.ts`
2. Add integration tests to `__tests__/handler.integration.test.ts`
3. Add service tests to `../shared/__tests__/email-service.test.ts`

### Testing Best Practices

- Always run mock tests first
- Use real email tests sparingly
- Test with various email content types
- Verify error handling scenarios
- Check performance under load

### Debugging

- Set `TEST_VERBOSE=true` for detailed logging
- Check AWS CloudWatch logs for Lambda execution details
- Verify SES sending statistics in AWS console
