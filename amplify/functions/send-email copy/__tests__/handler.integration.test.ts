/**
 * Integration tests for send-email handler
 *
 * These tests can run in two modes:
 * 1. Mock mode (default) - Uses mocked AWS SES
 * 2. Real mode - Sends actual emails via AWS SES
 *
 * To run with real emails:
 * TEST_MODE=real pnpm test amplify/functions/send-email/__tests__/handler.integration.test.ts
 *
 * To run with specific recipient:
 * TEST_MODE=real TEST_EMAIL=<EMAIL> pnpm test amplify/functions/send-email/__tests__/handler.integration.test.ts
 */

import { handler } from '../handler';

// Conditionally mock based on TEST_MODE environment variable
const isRealMode = process.env.TEST_MODE === 'real';
const testEmail = process.env.TEST_EMAIL || '<EMAIL>';

if (!isRealMode) {
  // Mock AWS SES for unit tests
  jest.mock('@aws-sdk/client-ses', () => ({
    SESClient: jest.fn().mockImplementation(() => ({
      send: jest.fn().mockResolvedValue({
        MessageId: 'mock-message-id-' + Date.now(),
        $metadata: {
          httpStatusCode: 200,
          requestId: 'mock-request-id',
        },
      }),
    })),
    SendEmailCommand: jest.fn().mockImplementation(params => params),
  }));
}

interface MockEvent {
  arguments: {
    to: string;
    subject: string;
    message: string;
    from?: string;
  };
  info?: {
    fieldName: string;
    parentTypeName: string;
  };
  request?: {
    headers: Record<string, string>;
  };
}

const createMockEvent = (
  args: Partial<MockEvent['arguments']> = {}
): MockEvent => ({
  arguments: {
    to: testEmail,
    subject: 'Integration Test Email',
    message: 'This is a test email from the integration test suite.',
    ...args,
  },
  info: {
    fieldName: 'sendEmail',
    parentTypeName: 'Query',
  },
  request: {
    headers: {
      'content-type': 'application/json',
    },
  },
});

describe(`send-email handler integration tests (${isRealMode ? 'REAL' : 'MOCK'} mode)`, () => {
  // Increase timeout for real email tests
  const testTimeout = isRealMode ? 30000 : 5000;

  beforeAll(() => {
    if (isRealMode) {
      console.log('🚨 RUNNING INTEGRATION TESTS WITH REAL EMAIL SENDING');
      console.log(`📧 Test emails will be sent to: ${testEmail}`);
      console.log('⚠️  Make sure AWS credentials are configured properly');
    } else {
      console.log('🧪 Running integration tests with mocked email service');
    }
  });

  describe('Basic email sending functionality', () => {
    it(
      'should send a basic test email',
      async () => {
        const event = createMockEvent({
          subject: `${isRealMode ? '[REAL]' : '[MOCK]'} Basic Test Email - ${new Date().toISOString()}`,
          message: `This is a basic test email sent from the integration test suite.
        
Test Details:
- Mode: ${isRealMode ? 'Real email sending' : 'Mocked email sending'}
- Timestamp: ${new Date().toISOString()}
- Test Type: Basic functionality test
- Handler: send-email Lambda function

If you received this email, the basic email sending functionality is working correctly!`,
        });

        const result = await handler(event);

        expect(result.success).toBe(true);
        expect(result.message).toBe('Email sent successfully via Amazon SES');
        expect(result.emailId).toBeDefined();
        expect(result.timestamp).toBeDefined();
        expect(result.sender).toBe('<EMAIL>');

        if (isRealMode) {
          console.log('✅ Real email sent successfully!');
          console.log(`📧 Email ID: ${result.emailId}`);
          console.log(`⏰ Timestamp: ${result.timestamp}`);
        }
      },
      testTimeout
    );

    it(
      'should send email with HTML content',
      async () => {
        const htmlMessage = `<h2>HTML Test Email</h2>
      <p>This email contains <strong>HTML formatting</strong> to test the branded template.</p>
      <ul>
        <li>✅ Bold text support</li>
        <li>✅ List formatting</li>
        <li>✅ Line breaks</li>
      </ul>
      <p style="color: #666;">This text should be gray.</p>`;

        const event = createMockEvent({
          subject: `${isRealMode ? '[REAL]' : '[MOCK]'} HTML Content Test - ${new Date().toISOString()}`,
          message: htmlMessage,
        });

        const result = await handler(event);

        expect(result.success).toBe(true);
        expect(result.emailId).toBeDefined();

        if (isRealMode) {
          console.log('✅ HTML email sent successfully!');
          console.log(`📧 Email ID: ${result.emailId}`);
        }
      },
      testTimeout
    );

    it(
      'should send email with special characters',
      async () => {
        const specialMessage = `Special Characters Test 🚀

This email tests various special characters:
- Emojis: 😀 🎉 ✨ 🔥 💯
- Accented characters: café, naïve, résumé
- Symbols: © ® ™ € $ £ ¥
- Math: α β γ δ ∑ ∏ ∞
- Arrows: → ← ↑ ↓ ⇒ ⇐

Unicode test: 你好世界 こんにちは مرحبا`;

        const event = createMockEvent({
          subject: `${isRealMode ? '[REAL]' : '[MOCK]'} Special Characters Test - ${new Date().toISOString()}`,
          message: specialMessage,
        });

        const result = await handler(event);

        expect(result.success).toBe(true);
        expect(result.emailId).toBeDefined();

        if (isRealMode) {
          console.log('✅ Special characters email sent successfully!');
          console.log(`📧 Email ID: ${result.emailId}`);
        }
      },
      testTimeout
    );
  });

  describe('Email template testing', () => {
    it(
      'should send email using branded template',
      async () => {
        const event = createMockEvent({
          subject: `${isRealMode ? '[REAL]' : '[MOCK]'} Branded Template Test - ${new Date().toISOString()}`,
          message: `This email tests the branded template functionality.

The email should include:
- Childfree Legacy branding
- Proper HTML formatting
- Styled background and layout
- Footer with application information

Template Type: Branded
Sent via: AWS Lambda + SES Integration`,
        });

        const result = await handler(event);

        expect(result.success).toBe(true);
        expect(result.emailId).toBeDefined();

        if (isRealMode) {
          console.log('✅ Branded template email sent successfully!');
          console.log(`📧 Email ID: ${result.emailId}`);
        }
      },
      testTimeout
    );
  });

  describe('Performance and reliability', () => {
    it(
      'should handle multiple concurrent email sends',
      async () => {
        const emailPromises = Array.from({ length: 3 }, (_, index) => {
          const event = createMockEvent({
            subject: `${isRealMode ? '[REAL]' : '[MOCK]'} Concurrent Test ${index + 1} - ${new Date().toISOString()}`,
            message: `This is concurrent email test #${index + 1}.
          
Testing the ability to handle multiple simultaneous email requests.
Email ${index + 1} of 3 in this batch.`,
          });
          return handler(event);
        });

        const results = await Promise.all(emailPromises);

        results.forEach((result, index) => {
          expect(result.success).toBe(true);
          expect(result.emailId).toBeDefined();

          if (isRealMode) {
            console.log(
              `✅ Concurrent email ${index + 1} sent: ${result.emailId}`
            );
          }
        });

        if (isRealMode) {
          console.log('✅ All concurrent emails sent successfully!');
        }
      },
      testTimeout * 2
    );

    it(
      'should complete email sending within reasonable time',
      async () => {
        const startTime = Date.now();

        const event = createMockEvent({
          subject: `${isRealMode ? '[REAL]' : '[MOCK]'} Performance Test - ${new Date().toISOString()}`,
          message:
            'This email tests the performance of the email sending functionality.',
        });

        const result = await handler(event);
        const endTime = Date.now();
        const duration = endTime - startTime;

        expect(result.success).toBe(true);
        expect(duration).toBeLessThan(isRealMode ? 10000 : 1000); // 10s for real, 1s for mock

        if (isRealMode) {
          console.log(`✅ Performance test completed in ${duration}ms`);
          console.log(`📧 Email ID: ${result.emailId}`);
        }
      },
      testTimeout
    );
  });

  if (isRealMode) {
    describe('Real email verification', () => {
      it(
        'should send a verification email with instructions',
        async () => {
          const event = createMockEvent({
            subject: `[REAL] Email Integration Test Verification - ${new Date().toISOString()}`,
            message: `🎉 Congratulations! Your email integration is working correctly.

This email confirms that:
✅ AWS SES is properly configured
✅ Lambda function is executing successfully  
✅ Email templates are rendering correctly
✅ All integration tests are passing

Test Summary:
- Function: send-email Lambda handler
- Service: Amazon SES
- Template: Branded template with HTML formatting
- Sender: <EMAIL>
- Recipient: ${testEmail}
- Test Time: ${new Date().toISOString()}

You can now confidently use the email functionality in your application!

---
This email was automatically generated by the integration test suite.`,
          });

          const result = await handler(event);

          expect(result.success).toBe(true);
          expect(result.emailId).toBeDefined();

          console.log('🎉 VERIFICATION EMAIL SENT SUCCESSFULLY!');
          console.log(`📧 Check your inbox at: ${testEmail}`);
          console.log(`📧 Email ID: ${result.emailId}`);
          console.log(`⏰ Sent at: ${result.timestamp}`);
        },
        testTimeout
      );
    });
  }
});
