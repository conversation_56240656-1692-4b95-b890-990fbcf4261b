import { handler } from '../handler';
import { sendEmail } from '../../../shared/email-service';

// Mock the shared email service
jest.mock('../../../shared/email-service', () => ({
  sendEmail: jest.fn(),
}));

const mockSendEmail = sendEmail as jest.MockedFunction<typeof sendEmail>;

// Mock event structure based on Amplify GraphQL schema
interface MockEvent {
  arguments: {
    to: string;
    subject: string;
    message: string;
    from?: string;
  };
  info?: {
    fieldName: string;
    parentTypeName: string;
  };
  request?: {
    headers: Record<string, string>;
  };
}

const createMockEvent = (
  args: Partial<MockEvent['arguments']> = {}
): MockEvent => ({
  arguments: {
    to: '<EMAIL>',
    subject: 'Test Subject',
    message: 'Test message content',
    ...args,
  },
  info: {
    fieldName: 'sendEmail',
    parentTypeName: 'Query',
  },
  request: {
    headers: {
      'content-type': 'application/json',
    },
  },
});

describe('send-email handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset console.log mock
    (console.log as jest.Mock).mockClear();
  });

  describe('Input validation', () => {
    it('should return error when "to" field is missing', async () => {
      const event = createMockEvent({ to: '' });

      const result = await handler(event);

      expect(result).toEqual({
        success: false,
        error: 'Missing required fields: to, subject, message',
      });
      expect(mockSendEmail).not.toHaveBeenCalled();
    });

    it('should return error when "subject" field is missing', async () => {
      const event = createMockEvent({ subject: '' });

      const result = await handler(event);

      expect(result).toEqual({
        success: false,
        error: 'Missing required fields: to, subject, message',
      });
      expect(mockSendEmail).not.toHaveBeenCalled();
    });

    it('should return error when "message" field is missing', async () => {
      const event = createMockEvent({ message: '' });

      const result = await handler(event);

      expect(result).toEqual({
        success: false,
        error: 'Missing required fields: to, subject, message',
      });
      expect(mockSendEmail).not.toHaveBeenCalled();
    });

    it('should return error when multiple fields are missing', async () => {
      const event = createMockEvent({ to: '', subject: '', message: '' });

      const result = await handler(event);

      expect(result).toEqual({
        success: false,
        error: 'Missing required fields: to, subject, message',
      });
      expect(mockSendEmail).not.toHaveBeenCalled();
    });

    it('should accept valid input with all required fields', async () => {
      mockSendEmail.mockResolvedValue({
        success: true,
        messageId: 'test-message-id',
      });

      const event = createMockEvent({
        to: '<EMAIL>',
        subject: 'Valid Subject',
        message: 'Valid message content',
      });

      const result = await handler(event);

      expect(result.success).toBe(true);
      expect(mockSendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Valid Subject',
        message: 'Valid message content',
        templateType: 'branded',
      });
    });
  });

  describe('Successful email sending', () => {
    it('should return success response when email is sent successfully', async () => {
      const mockMessageId = 'test-message-id-123';
      mockSendEmail.mockResolvedValue({
        success: true,
        messageId: mockMessageId,
      });

      const event = createMockEvent();

      const result = await handler(event);

      expect(result).toEqual({
        success: true,
        message: 'Email sent successfully via Amazon SES',
        emailId: mockMessageId,
        timestamp: expect.any(String),
        sender: '<EMAIL>',
      });
      expect(result.timestamp).toMatch(
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/
      );
    });

    it('should generate fallback emailId when messageId is not provided', async () => {
      mockSendEmail.mockResolvedValue({
        success: true,
        // No messageId provided
      });

      const event = createMockEvent();

      const result = await handler(event);

      expect(result.success).toBe(true);
      expect(result.emailId).toMatch(/^email_\d+$/);
    });

    it('should call sendEmail with correct parameters', async () => {
      mockSendEmail.mockResolvedValue({
        success: true,
        messageId: 'test-id',
      });

      const event = createMockEvent({
        to: '<EMAIL>',
        subject: 'Test Email Subject',
        message: 'This is a test email message',
      });

      await handler(event);

      expect(mockSendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Test Email Subject',
        message: 'This is a test email message',
        templateType: 'branded',
      });
    });
  });

  describe('Email sending failures', () => {
    it('should handle sendEmail service failure', async () => {
      mockSendEmail.mockResolvedValue({
        success: false,
        error: 'SES service error',
        details: 'Rate limit exceeded',
      });

      const event = createMockEvent();

      const result = await handler(event);

      expect(result).toEqual({
        success: false,
        error: 'SES service error',
        details: 'Rate limit exceeded',
      });
    });

    it('should handle sendEmail service failure with minimal error info', async () => {
      mockSendEmail.mockResolvedValue({
        success: false,
      });

      const event = createMockEvent();

      const result = await handler(event);

      expect(result).toEqual({
        success: false,
        error: 'Failed to send email',
        details: 'Unknown error',
      });
    });
  });

  describe('Exception handling', () => {
    it('should handle thrown exceptions from sendEmail', async () => {
      const errorMessage = 'Network connection failed';
      mockSendEmail.mockRejectedValue(new Error(errorMessage));

      const event = createMockEvent();

      const result = await handler(event);

      expect(result).toEqual({
        success: false,
        error: 'Failed to send email',
        details: errorMessage,
      });
    });

    it('should handle non-Error exceptions', async () => {
      mockSendEmail.mockRejectedValue('String error');

      const event = createMockEvent();

      const result = await handler(event);

      expect(result).toEqual({
        success: false,
        error: 'Failed to send email',
        details: 'Unknown error',
      });
    });
  });

  describe('Logging behavior', () => {
    it('should log the incoming event', async () => {
      mockSendEmail.mockResolvedValue({
        success: true,
        messageId: 'test-id',
      });

      const event = createMockEvent();

      await handler(event);

      expect(console.log).toHaveBeenCalledWith(
        'Email send request received:',
        JSON.stringify(event, null, 2)
      );
    });

    it('should log successful response', async () => {
      mockSendEmail.mockResolvedValue({
        success: true,
        messageId: 'test-id',
      });

      const event = createMockEvent();

      await handler(event);

      expect(console.log).toHaveBeenCalledWith(
        'Returning response:',
        expect.objectContaining({
          success: true,
          message: 'Email sent successfully via Amazon SES',
        })
      );
    });

    it('should log error responses', async () => {
      mockSendEmail.mockResolvedValue({
        success: false,
        error: 'Test error',
      });

      const event = createMockEvent();

      await handler(event);

      expect(console.log).toHaveBeenCalledWith(
        'Returning error response:',
        expect.objectContaining({
          success: false,
          error: 'Test error',
        })
      );
    });
  });
});
