// @ts-nocheck

import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import type { Schema } from '../../data/resource';
import { getAmplifyDataClientConfig } from '@aws-amplify/backend/function/runtime';
import { Amplify } from 'aws-amplify';
import { generateClient } from 'aws-amplify/data';
import { env } from '$amplify/env/sendEmail';

// Initialize SES client
const sesClient = new SESClient({
  region: process.env.SES_REGION || 'us-east-1',
});

// Initialize Amplify client for database operations
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>({
  authMode: 'iam',
});

export const handler = async event => {
  console.log('Email send request:', JSON.stringify(event, null, 2));

  try {
    // Extract data from event - handle both direct Lambda calls and GraphQL/AppSync calls
    const { to, subject, message, verificationLink, emailType, isNewAccount } =
      event.arguments || event;

    // Validate required fields
    if (!to || !subject || !message) {
      throw new Error('Missing required fields: to, subject, message');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to)) {
      throw new Error('Invalid email format');
    }

    // Generate verification token if this is for account confirmation or password reset and no link provided
    let finalVerificationLink = verificationLink;
    if (
      (emailType === 'accountConfirmation' || emailType === 'passwordReset') &&
      (!verificationLink || isNewAccount)
    ) {
      console.log(
        'Generating verification token for email type:',
        emailType,
        'for email:',
        to
      );
      try {
        const tokenResult = await client.mutations.generateVerificationToken({
          email: to,
          verificationType: emailType,
        });

        const tokenData = tokenResult.data as any;

        // Try to parse if it's a string
        let parsedTokenData = tokenData;
        if (typeof tokenData === 'string') {
          try {
            parsedTokenData = JSON.parse(tokenData);
            console.log('Parsed token data:', parsedTokenData);
          } catch (e) {
            console.log('Failed to parse token data as JSON:', e);
          }
        }

        if (parsedTokenData?.success) {
          finalVerificationLink = parsedTokenData.verificationLink;
          console.log('Generated verification link:', finalVerificationLink);
        } else {
          throw new Error(
            parsedTokenData?.error || 'Failed to generate verification token'
          );
        }
      } catch (tokenError) {
        console.error('Error generating verification token:', tokenError);
        throw new Error(
          `Failed to generate verification token: ${tokenError.message}`
        );
      }
    }

    // Prepare email parameters based on email type
    let emailParams;

    switch (emailType) {
      case 'accountConfirmation':
        emailParams = {
          Source: process.env.FROM_EMAIL || '<EMAIL>',
          Destination: {
            ToAddresses: [to],
          },
          Message: {
            Subject: {
              Data: subject,
              Charset: 'UTF-8',
            },
            Body: {
              Text: {
                Data: message,
                Charset: 'UTF-8',
              },
              Html: {
                Data: `
                  <html>
                    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #2563eb;">Childfree</h2>
                        <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px;">
                          <h3>Welcome to Childfree!</h3>
                          <p>Please verify your email address by clicking the button below to complete your registration.</p>
                          <p>This link will expire in 24 hours.</p>
                          ${
                            finalVerificationLink
                              ? `
                            <div style="margin-top: 20px; text-align: center;">
                              <a href="${finalVerificationLink}"
                                 style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                                Verify Your Email
                              </a>
                            </div>
                            <p style="margin-top: 15px; font-size: 14px; color: #6b7280;">
                              Or copy and paste this link in your browser:<br>
                              <a href="${finalVerificationLink}" style="color: #2563eb; word-break: break-all;">${finalVerificationLink}</a>
                            </p>
                          `
                              : ''
                          }
                        </div>
                        <p style="color: #6b7280; font-size: 12px; margin-top: 20px;">
                          This email was sent from Childfree application.
                        </p>
                      </div>
                    </body>
                  </html>
                `,
                Charset: 'UTF-8',
              },
            },
          },
        };
        break;

      case 'passwordReset':
        // Generate reset link for password reset
        const resetLink = finalVerificationLink
          ? finalVerificationLink.replace(
              '/auth/verify-email',
              '/auth/reset-password'
            )
          : '';

        emailParams = {
          Source: process.env.FROM_EMAIL || '<EMAIL>',
          Destination: {
            ToAddresses: [to],
          },
          Message: {
            Subject: {
              Data: subject,
              Charset: 'UTF-8',
            },
            Body: {
              Text: {
                Data: message,
                Charset: 'UTF-8',
              },
              Html: {
                Data: `
                  <html>
                    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #2563eb;">Childfree</h2>
                        <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px;">
                          <h3>Password Reset Request</h3>
                          <p>You requested to reset your password. Please click the button below to set a new password.</p>
                          <p>This link will expire in 24 hours.</p>
                          ${
                            resetLink
                              ? `
                            <div style="margin-top: 20px; text-align: center;">
                              <a href="${resetLink}"
                                 style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                                Reset Password
                              </a>
                            </div>
                            <p style="margin-top: 15px; font-size: 14px; color: #6b7280;">
                              Or copy and paste this link in your browser:<br>
                              <a href="${resetLink}" style="color: #2563eb; word-break: break-all;">${resetLink}</a>
                            </p>
                          `
                              : ''
                          }
                          <p style="margin-top: 15px; font-size: 14px; color: #6b7280;">
                            If you didn't request this password reset, please ignore this email.
                          </p>
                        </div>
                        <p style="color: #6b7280; font-size: 12px; margin-top: 20px;">
                          This email was sent from Childfree application.
                        </p>
                      </div>
                    </body>
                  </html>
                `,
                Charset: 'UTF-8',
              },
            },
          },
        };
        break;

      case 'notification':
        emailParams = {
          Source: process.env.FROM_EMAIL || '<EMAIL>',
          Destination: {
            ToAddresses: [to],
          },
          Message: {
            Subject: {
              Data: 'Notification - Childfree (Placeholder)',
              Charset: 'UTF-8',
            },
            Body: {
              Text: {
                Data: 'Notification functionality not implemented yet.',
                Charset: 'UTF-8',
              },
              Html: {
                Data: `
                  <html>
                    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #2563eb;">Childfree</h2>
                        <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px;">
                          <p>Notification functionality not implemented yet.</p>
                        </div>
                        <p style="color: #6b7280; font-size: 12px; margin-top: 20px;">
                          This email was sent from Childfree application.
                        </p>
                      </div>
                    </body>
                  </html>
                `,
                Charset: 'UTF-8',
              },
            },
          },
        };
        break;

      default:
        // Fallback to original behavior if no emailType or unknown emailType
        emailParams = {
          Source: process.env.FROM_EMAIL || '<EMAIL>',
          Destination: {
            ToAddresses: [to],
          },
          Message: {
            Subject: {
              Data: subject,
              Charset: 'UTF-8',
            },
            Body: {
              Text: {
                Data: message,
                Charset: 'UTF-8',
              },
              Html: {
                Data: `
                  <html>
                    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #2563eb;">Childfree</h2>
                        <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px;">
                          ${message.replace(/\n/g, '<br>')}
                          ${
                            finalVerificationLink
                              ? `
                            <div style="margin-top: 20px; text-align: center;">
                              <a href="${finalVerificationLink}"
                                 style="display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                                Verify Your Email
                              </a>
                            </div>
                            <p style="margin-top: 15px; font-size: 14px; color: #6b7280;">
                              Or copy and paste this link in your browser:<br>
                              <a href="${finalVerificationLink}" style="color: #2563eb; word-break: break-all;">${finalVerificationLink}</a>
                            </p>
                          `
                              : ''
                          }
                        </div>
                        <p style="color: #6b7280; font-size: 12px; margin-top: 20px;">
                          This email was sent from Childfree application.
                        </p>
                      </div>
                    </body>
                  </html>
                `,
                Charset: 'UTF-8',
              },
            },
          },
        };
        break;
    }

    // Send email
    const command = new SendEmailCommand(emailParams);
    const result = await sesClient.send(command);

    console.log('Email sent successfully:', result.MessageId);

    return {
      success: true,
      messageId: result.MessageId,
      message: 'Email sent successfully',
    };
  } catch (error) {
    console.error('Error sending email:', error);

    return {
      success: false,
      error: error.message || 'Failed to send email',
    };
  }
};
