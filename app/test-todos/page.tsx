import TodoExample from '@/components/TodoExample';

export default function TestTodosPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Todo API Test Page
        </h1>
        <p className="text-gray-600">
          This page demonstrates the simple Next.js API call to fetch and create todos
        </p>
      </div>
      
      <TodoExample />
      
      <div className="max-w-md mx-auto mt-8 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">API Endpoints:</h3>
        <ul className="text-sm space-y-1">
          <li><code className="bg-white px-2 py-1 rounded">GET /api/todos</code> - Fetch all todos</li>
          <li><code className="bg-white px-2 py-1 rounded">POST /api/todos</code> - Create a new todo</li>
        </ul>
        
        <h3 className="font-semibold mt-4 mb-2">Example Usage:</h3>
        <pre className="text-xs bg-white p-2 rounded overflow-x-auto">
{`// Fetch todos
const response = await fetch('/api/todos');
const { data: todos } = await response.json();

// Create todo
const response = await fetch('/api/todos', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ content: 'My todo' })
});`}
        </pre>
      </div>
    </div>
  );
}
