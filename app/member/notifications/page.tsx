'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Bell,
  Search,
  Filter,
  CheckCheck,
  Loader2,
  AlertCircle,
  Settings,
  AlertTriangle,
  Clock,
  Info,
  Zap,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import Link from 'next/link';
import { useNotifications } from '@/lib/notifications/notification-context';
import { NotificationItem } from '@/components/notifications/notification-item';
import {
  NotificationCategory,
  NotificationPriority,
  NotificationStatus,
  NotificationFilters,
} from '@/types/notifications';

export default function MemberNotificationsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState<NotificationFilters>({});
  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);
  const [isFiltersExpanded, setIsFiltersExpanded] = useState(false);

  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAllAsRead,
    fetchNotifications,
  } = useNotifications();

  // Filter notifications based on search and filters
  const filteredNotifications = notifications.filter(notification => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Category filter
    if (
      activeFilters.category &&
      notification.category !== activeFilters.category
    ) {
      return false;
    }

    // Priority filter
    if (
      activeFilters.priority &&
      notification.priority !== activeFilters.priority
    ) {
      return false;
    }

    // Status filter
    if (activeFilters.status && notification.status !== activeFilters.status) {
      return false;
    }

    // Unread only filter
    if (
      activeFilters.unreadOnly &&
      notification.status === NotificationStatus.READ
    ) {
      return false;
    }

    return true;
  });

  const handleFilterChange = async (
    key: keyof NotificationFilters,
    value: any
  ) => {
    const newFilters = { ...activeFilters, [key]: value };
    setActiveFilters(newFilters);
    await fetchNotifications(newFilters);
  };

  const handleClearFilters = async () => {
    setActiveFilters({});
    setSearchTerm('');
    await fetchNotifications();
  };

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) return;

    setIsMarkingAllRead(true);
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all as read:', error);
    } finally {
      setIsMarkingAllRead(false);
    }
  };

  // Handle stat card clicks for filtering
  const handleStatCardClick = async (filterType: string) => {
    let newFilters: NotificationFilters = {};

    switch (filterType) {
      case 'total':
        // Clear all filters to show all notifications
        newFilters = {};
        break;
      case 'unread':
        newFilters = { unreadOnly: true };
        break;
      case 'action_required':
        newFilters = { category: NotificationCategory.ACTION_REQUIRED };
        break;
      case 'urgent':
        newFilters = { category: NotificationCategory.URGENT };
        break;
    }

    setActiveFilters(newFilters);
    setSearchTerm('');
    await fetchNotifications(newFilters);
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='space-y-4'>
        <div>
          <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] mb-2'>
            Notifications
          </h1>
          <p className='text-lg text-[var(--custom-gray-medium)]'>
            Stay updated with your estate planning activities
          </p>
        </div>

        {/* Action Buttons */}
        <div className='flex items-center justify-end space-x-3'>
          {unreadCount > 0 && (
            <Button
              onClick={handleMarkAllAsRead}
              disabled={isMarkingAllRead}
              variant='outline'
              className='cursor-pointer'
              title='Mark all notifications as read'
            >
              {isMarkingAllRead ? (
                <Loader2 className='h-4 w-4 animate-spin mr-2' />
              ) : (
                <CheckCheck className='h-4 w-4 mr-2' />
              )}
              Mark all read
            </Button>
          )}

          <Link href='/dashboard/settings'>
            <Button
              variant='outline'
              className='cursor-pointer'
              title='Customize which notifications you want to receive'
            >
              <Settings className='h-4 w-4 mr-2' />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Interactive Stats Cards */}
      <div className='grid md:grid-cols-4 gap-4'>
        {/* Total Notifications */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            Object.keys(activeFilters).length === 0 && !searchTerm
              ? 'ring-2 ring-blue-500 bg-blue-50'
              : 'hover:bg-gray-50'
          }`}
          onClick={() => handleStatCardClick('total')}
          title='Click to view all notifications'
        >
          <CardContent className='p-4'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-blue-100 rounded-lg'>
                <Bell className='h-6 w-6 text-blue-600' />
              </div>
              <div className='flex-1'>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Total
                </p>
                <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                  {notifications.length}
                </p>
              </div>
              {Object.keys(activeFilters).length === 0 && !searchTerm && (
                <Badge
                  variant='outline'
                  className='bg-blue-100 text-blue-700 border-blue-300'
                >
                  Active
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Unread Notifications */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            activeFilters.unreadOnly
              ? 'ring-2 ring-red-500 bg-red-50'
              : 'hover:bg-gray-50'
          }`}
          onClick={() => handleStatCardClick('unread')}
          title='Click to view only unread notifications'
        >
          <CardContent className='p-4'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-red-100 rounded-lg'>
                <AlertCircle className='h-6 w-6 text-red-600' />
              </div>
              <div className='flex-1'>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Unread
                </p>
                <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                  {unreadCount}
                </p>
              </div>
              {activeFilters.unreadOnly && (
                <Badge
                  variant='outline'
                  className='bg-red-100 text-red-700 border-red-300'
                >
                  Active
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Action Required */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            activeFilters.category === NotificationCategory.ACTION_REQUIRED
              ? 'ring-2 ring-yellow-500 bg-yellow-50'
              : 'hover:bg-gray-50'
          }`}
          onClick={() => handleStatCardClick('action_required')}
          title='Click to view only notifications requiring action'
        >
          <CardContent className='p-4'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-yellow-100 rounded-lg'>
                <Clock className='h-6 w-6 text-yellow-600' />
              </div>
              <div className='flex-1'>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Action Required
                </p>
                <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                  {
                    notifications.filter(
                      n => n.category === NotificationCategory.ACTION_REQUIRED
                    ).length
                  }
                </p>
              </div>
              {activeFilters.category ===
                NotificationCategory.ACTION_REQUIRED && (
                <Badge
                  variant='outline'
                  className='bg-yellow-100 text-yellow-700 border-yellow-300'
                >
                  Active
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Urgent Notifications */}
        <Card
          className={`cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${
            activeFilters.category === NotificationCategory.URGENT
              ? 'ring-2 ring-red-600 bg-red-50'
              : 'hover:bg-gray-50'
          }`}
          onClick={() => handleStatCardClick('urgent')}
          title='Click to view only urgent notifications'
        >
          <CardContent className='p-4'>
            <div className='flex items-center space-x-3'>
              <div className='p-2 bg-red-100 rounded-lg'>
                <Zap className='h-6 w-6 text-red-600' />
              </div>
              <div className='flex-1'>
                <p className='text-sm font-medium text-[var(--custom-gray-medium)]'>
                  Urgent
                </p>
                <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                  {
                    notifications.filter(
                      n => n.category === NotificationCategory.URGENT
                    ).length
                  }
                </p>
              </div>
              {activeFilters.category === NotificationCategory.URGENT && (
                <Badge
                  variant='outline'
                  className='bg-red-100 text-red-700 border-red-300'
                >
                  Active
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sticky Filters Panel */}
      <div className='sticky top-4 z-10'>
        <Card className='shadow-lg'>
          <CardHeader className='pb-3'>
            <div className='flex items-center justify-between'>
              <CardTitle className='flex items-center space-x-2'>
                <Filter className='h-5 w-5' />
                <span>Search & Filters</span>
              </CardTitle>
              <div className='flex items-center space-x-3'>
                {(Object.keys(activeFilters).length > 0 || searchTerm) && (
                  <Badge
                    variant='outline'
                    className='bg-blue-100 text-blue-700 border-blue-300'
                  >
                    {Object.keys(activeFilters).length + (searchTerm ? 1 : 0)}{' '}
                    active
                  </Badge>
                )}
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => setIsFiltersExpanded(!isFiltersExpanded)}
                  className='flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground'
                  title={
                    isFiltersExpanded
                      ? 'Hide advanced filters'
                      : 'Show advanced filters'
                  }
                >
                  {isFiltersExpanded ? (
                    <>
                      <ChevronUp className='h-4 w-4' />
                      Hide Filters
                    </>
                  ) : (
                    <>
                      <ChevronDown className='h-4 w-4' />
                      Show Filters
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Always visible search */}
            <div className='relative mb-4'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--custom-gray-medium)]' />
              <Input
                placeholder='Search notifications...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Advanced filters - collapsible */}
            {isFiltersExpanded && (
              <div className='space-y-4'>
                <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                  {/* Category Filter */}
                  <Select
                    value={activeFilters.category || ''}
                    onValueChange={value =>
                      handleFilterChange('category', value || undefined)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='All categories' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value=''>All categories</SelectItem>
                      <SelectItem value={NotificationCategory.URGENT}>
                        Urgent
                      </SelectItem>
                      <SelectItem value={NotificationCategory.ACTION_REQUIRED}>
                        Action Required
                      </SelectItem>
                      <SelectItem value={NotificationCategory.INFORMATIONAL}>
                        Informational
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Priority Filter */}
                  <Select
                    value={activeFilters.priority || ''}
                    onValueChange={value =>
                      handleFilterChange('priority', value || undefined)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='All priorities' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value=''>All priorities</SelectItem>
                      <SelectItem value={NotificationPriority.HIGH}>
                        High
                      </SelectItem>
                      <SelectItem value={NotificationPriority.MEDIUM}>
                        Medium
                      </SelectItem>
                      <SelectItem value={NotificationPriority.LOW}>
                        Low
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  {/* Status Filter */}
                  <Select
                    value={activeFilters.status || ''}
                    onValueChange={value =>
                      handleFilterChange('status', value || undefined)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='All statuses' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value=''>All statuses</SelectItem>
                      <SelectItem value={NotificationStatus.READ}>
                        Read
                      </SelectItem>
                      <SelectItem value={NotificationStatus.DELIVERED}>
                        Unread
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Active Filters */}
                {(Object.keys(activeFilters).length > 0 || searchTerm) && (
                  <div className='flex items-center space-x-2 pt-4 border-t'>
                    <span className='text-sm text-[var(--custom-gray-medium)]'>
                      Active filters:
                    </span>
                    {searchTerm && (
                      <Badge variant='secondary'>Search: {searchTerm}</Badge>
                    )}
                    {activeFilters.category && (
                      <Badge variant='secondary'>
                        Category: {activeFilters.category}
                      </Badge>
                    )}
                    {activeFilters.priority && (
                      <Badge variant='secondary'>
                        Priority: {activeFilters.priority}
                      </Badge>
                    )}
                    {activeFilters.status && (
                      <Badge variant='secondary'>
                        Status: {activeFilters.status}
                      </Badge>
                    )}
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={handleClearFilters}
                      className='cursor-pointer'
                      title='Clear all active filters'
                    >
                      Clear all
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Quick hint when filters are collapsed */}
            {!isFiltersExpanded &&
              (Object.keys(activeFilters).length > 0 || searchTerm) && (
                <div className='flex items-center justify-between text-sm text-muted-foreground'>
                  <span>
                    {Object.keys(activeFilters).length + (searchTerm ? 1 : 0)}{' '}
                    filter(s) active
                  </span>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={handleClearFilters}
                    className='cursor-pointer text-xs'
                    title='Clear all active filters'
                  >
                    Clear all
                  </Button>
                </div>
              )}
          </CardContent>
        </Card>
      </div>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle>Notifications ({filteredNotifications.length})</CardTitle>
        </CardHeader>
        <CardContent className='p-0'>
          {/* Loading State */}
          {isLoading && (
            <div className='flex items-center justify-center py-8'>
              <Loader2 className='h-6 w-6 animate-spin text-[var(--custom-gray-medium)]' />
              <span className='ml-2 text-sm text-[var(--custom-gray-medium)]'>
                Loading notifications...
              </span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className='flex items-center justify-center py-8 px-4'>
              <AlertCircle className='h-5 w-5 text-red-500 mr-2' />
              <span className='text-sm text-red-600'>{error}</span>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !error && filteredNotifications.length === 0 && (
            <div className='flex flex-col items-center justify-center py-12 px-4 text-center'>
              <Bell className='h-16 w-16 text-[var(--custom-gray-light)] mb-4' />
              <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                No notifications found
              </h3>
              <p className='text-[var(--custom-gray-medium)] mb-4'>
                {notifications.length === 0
                  ? "You don't have any notifications yet."
                  : 'No notifications match your current filters.'}
              </p>
              {Object.keys(activeFilters).length > 0 || searchTerm ? (
                <Button
                  variant='outline'
                  onClick={handleClearFilters}
                  className='cursor-pointer'
                >
                  Clear filters
                </Button>
              ) : null}
            </div>
          )}

          {/* Notifications */}
          {!isLoading && !error && filteredNotifications.length > 0 && (
            <div>
              {filteredNotifications.map(notification => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  showActions={true}
                  compact={false}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
