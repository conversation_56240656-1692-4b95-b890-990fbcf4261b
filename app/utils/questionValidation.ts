/**
 * Question validation utilities for interview answers
 */

export type QuestionValidationType = 'number' | 'email' | 'phone';

export interface ValidationResult {
  isValid: boolean;
  errorMessage?: string;
}

/**
 * Validate a number input
 */
export function validateNumber(value: string): ValidationResult {
  if (!value || value.trim() === '') {
    return { isValid: true }; // Empty values are allowed
  }

  const trimmedValue = value.trim();

  // Check if it's a valid number
  if (isNaN(Number(trimmedValue))) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid number',
    };
  }

  // Check for valid number format (no leading/trailing spaces, no multiple decimals)
  const numberRegex = /^-?\d*\.?\d+$/;
  if (!numberRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid number',
    };
  }

  return { isValid: true };
}

/**
 * Validate an email input
 */
export function validateEmail(value: string): ValidationResult {
  if (!value || value.trim() === '') {
    return { isValid: true }; // Empty values are allowed
  }

  const trimmedValue = value.trim();

  // Basic email regex pattern
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!emailRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid email address',
    };
  }

  // Additional checks for common email issues
  if (trimmedValue.length > 255) {
    return {
      isValid: false,
      errorMessage: 'Email address is too long',
    };
  }

  if (
    trimmedValue.includes('..') ||
    trimmedValue.startsWith('.') ||
    trimmedValue.endsWith('.')
  ) {
    return {
      isValid: false,
      errorMessage: 'Please enter a valid email address',
    };
  }

  return { isValid: true };
}

/**
 * Validate a phone number input
 */
export function validatePhone(value: string): ValidationResult {
  if (!value || value.trim() === '') {
    return { isValid: true }; // Empty values are allowed
  }

  const trimmedValue = value.trim();

  // US phone number regex - supports various formats
  const phoneRegex =
    /^(\+1\s?)?(\([0-9]{3}\)|[0-9]{3})[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}$/;

  if (!phoneRegex.test(trimmedValue)) {
    return {
      isValid: false,
      errorMessage:
        'Please enter a valid US phone number (e.g., (*************)',
    };
  }

  return { isValid: true };
}

/**
 * Main validation function that routes to specific validators
 */
export function validateQuestionAnswer(
  value: string,
  validationType?: QuestionValidationType
): ValidationResult {
  if (!validationType) {
    return { isValid: true }; // No validation required
  }

  switch (validationType) {
    case 'number':
      return validateNumber(value);
    case 'email':
      return validateEmail(value);
    case 'phone':
      return validatePhone(value);
    default:
      return { isValid: true };
  }
}

/**
 * Get user-friendly validation rule description
 */
export function getValidationDescription(
  validationType?: QuestionValidationType
): string {
  switch (validationType) {
    case 'number':
      return 'Only numeric values are allowed';
    case 'email':
      return 'Please enter a valid email address';
    case 'phone':
      return 'Please enter a valid US phone number';
    default:
      return '';
  }
}
