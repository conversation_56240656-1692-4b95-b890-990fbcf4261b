import type { Schema } from '@/amplify/data/resource';
import { generateClient } from 'aws-amplify/data';

type Template = Schema['Template']['type'];
type TemplateVersion = Schema['TemplateVersion']['type'];

interface TemplateWithVersion extends Template {
  latestVersion?: TemplateVersion;
}

/**
 * Fetches all templates with their latest versions
 * @returns Promise<TemplateWithVersion[]> Array of templates with their latest versions
 * @throws Error if there's an issue fetching templates
 */
export const fetchTemplates = async (): Promise<TemplateWithVersion[]> => {
  try {
    const client = generateClient<Schema>();
    const { data: templatesData } = await client.models.Template.list({
      filter: {
        isActive: { eq: true },
        isDraft: { eq: false },
      },
    });

    // Fetch all versions for all templates in parallel
    const templatesWithVersions = await Promise.all(
      templatesData.map(async template => {
        const { data: versions } = await client.models.TemplateVersion.list({
          filter: {
            templateId: { eq: template.id },
          },
        });

        // Get the latest version by sorting once
        const latestVersion =
          versions.length > 0
            ? versions.reduce((latest, current) =>
                current.versionNumber > latest.versionNumber ? current : latest
              )
            : undefined;

        return {
          ...template,
          latestVersion,
        };
      })
    );

    return templatesWithVersions;
  } catch (error) {
    console.error('Error fetching templates:', error);
    throw new Error('Failed to fetch templates');
  }
};

/**
 * Fetches a specific template and its latest version
 * @param templateId - The ID of the template to fetch
 * @returns Promise<{template: Template, latestVersion: TemplateVersion | undefined}>
 * @throws Error if template is not found or there's an issue fetching data
 */
export async function getTemplate(templateId: string) {
  try {
    const client = generateClient<Schema>();

    const { data: template } = await client.models.Template.get({
      id: templateId,
    });

    if (!template) {
      throw new Error(`Template not found with ID: ${templateId}`);
    }

    const { data: versions } = await client.models.TemplateVersion.list({
      filter: {
        templateId: { eq: templateId },
      },
    });

    // Get the latest version using reduce instead of sort
    const latestVersion =
      versions.length > 0
        ? versions.reduce((latest, current) =>
            current.versionNumber > latest.versionNumber ? current : latest
          )
        : undefined;

    return {
      template,
      latestVersion,
    };
  } catch (error) {
    console.error(`Error fetching template ${templateId}:`, error);
    throw error;
  }
}

/**
 * Fetches all versions for a specific template, sorted by version number (newest first)
 * @param templateId - The ID of the template to fetch versions for
 * @returns Promise<TemplateVersion[]> Array of all template versions
 * @throws Error if there's an issue fetching template versions
 */
export async function getTemplateVersions(
  templateId: string
): Promise<TemplateVersion[]> {
  try {
    const client = generateClient<Schema>();

    const { data: versions } = await client.models.TemplateVersion.list({
      filter: {
        templateId: { eq: templateId },
      },
    });

    // Sort versions by version number in descending order (newest first)
    return versions.sort((a, b) => b.versionNumber - a.versionNumber);
  } catch (error) {
    console.error(`Error fetching template versions for ${templateId}:`, error);
    throw new Error('Failed to fetch template versions');
  }
}

/**
 * Fetches a template with all its versions for the history page
 * @param templateId - The ID of the template to fetch
 * @returns Promise<{template: Template, versions: TemplateVersion[]}>
 * @throws Error if template is not found or there's an issue fetching data
 */
export async function getTemplateWithHistory(templateId: string) {
  try {
    const [templateResult, versions] = await Promise.all([
      getTemplate(templateId),
      getTemplateVersions(templateId),
    ]);

    return {
      template: templateResult.template,
      versions,
    };
  } catch (error) {
    console.error(`Error fetching template with history ${templateId}:`, error);
    throw error;
  }
}

/**
 * Updates a template's active status (archive/unarchive)
 * @param templateId - The ID of the template to update
 * @param isActive - Boolean indicating whether to archive (false) or unarchive (true)
 * @returns Promise<Template> The updated template
 * @throws Error if template is not found or there's an issue updating
 */
export async function updateTemplateStatus(
  templateId: string,
  isActive: boolean
): Promise<Template> {
  try {
    const client = generateClient<Schema>();
    const { data: template } = await client.models.Template.update({
      id: templateId,
      isActive,
    });

    if (!template) {
      throw new Error(`Template not found with ID: ${templateId}`);
    }

    return template;
  } catch (error) {
    console.error(`Error updating template ${templateId} status:`, error);
    throw error;
  }
}
