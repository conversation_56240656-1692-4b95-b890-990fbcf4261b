'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SubscriptionCard } from '@/components/billing/subscription-card';
import { PaymentMethodForm } from '@/components/billing/payment-method-form';
import { AlertCircle, ArrowLeft, CheckCircle2 } from 'lucide-react';
import { Headline, Subhead } from '../../../../components/ui/brand/typography';
import {
  mockPricingInfo,
  mockUserBillingInfo,
} from '@/components/billing/mock-data';
import {
  BillingCycle,
  PricingInfo,
  SubscriptionTier,
  UserBillingInfo,
} from '@/components/billing/types';

export default function SubscribePage() {
  const router = useRouter();
  const [billingInfo, setBillingInfo] = useState<UserBillingInfo | null>(null);
  const [pricingInfo, setPricingInfo] = useState<PricingInfo[]>([]);
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier | null>(
    null
  );
  const [selectedCycle, setSelectedCycle] = useState<BillingCycle>('Monthly');
  const [step, setStep] = useState(1);
  const [discountCode, setDiscountCode] = useState('');
  const [discountApplied, setDiscountApplied] = useState(false);
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);
  const [, setIsProcessing] = useState(false);

  useEffect(() => {
    // In a real implementation, this would fetch data from an API
    // For now, we'll use mock data
    setBillingInfo(mockUserBillingInfo);
    setPricingInfo(mockPricingInfo);
  }, []);

  const handleSelectTier = (tier: SubscriptionTier) => {
    setSelectedTier(tier);
  };

  const handleCycleChange = (cycle: BillingCycle) => {
    setSelectedCycle(cycle);
  };

  const handleApplyDiscount = () => {
    if (!discountCode.trim()) {
      setAlert({
        type: 'error',
        message: 'Please enter a discount code.',
      });
      return;
    }

    // In a real implementation, this would validate the discount code with an API
    if (discountCode.toUpperCase() === 'WELCOME10') {
      setDiscountApplied(true);
      setAlert({
        type: 'success',
        message:
          'Discount code applied successfully! 10% off your subscription.',
      });
    } else {
      setAlert({
        type: 'error',
        message: 'Invalid discount code. Please try again.',
      });
    }

    // Clear alert after 5 seconds
    setTimeout(() => setAlert(null), 5000);
  };

  const handleContinueToPayment = () => {
    if (!selectedTier) {
      setAlert({
        type: 'error',
        message: 'Please select a subscription plan.',
      });
      return;
    }

    setStep(2);
    setAlert(null);
  };

  const handleSubmitPayment = (_paymentData: any) => {
    setIsProcessing(true);

    // In a real implementation, this would call an API to process the payment and create the subscription
    setTimeout(() => {
      setIsProcessing(false);
      setStep(3);
    }, 2000);
  };

  const handleFinish = () => {
    router.push('/dashboard/billing');
  };

  if (!billingInfo || pricingInfo.length === 0) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <div className='flex items-center justify-center h-64'>
            <p className='text-muted-foreground'>
              Loading subscription information...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <Button
            variant='outline'
            onClick={() => router.push('/dashboard/billing')}
            className='mb-4'
          >
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back to Billing
          </Button>
          <Headline className='mb-2'>Subscribe to Childfree Legacy</Headline>
          <Subhead className='text-muted-foreground'>
            Choose a plan that works for you.
          </Subhead>
        </div>

        {alert && (
          <Alert
            className={`mb-6 ${
              alert.type === 'success'
                ? 'bg-green-50 text-green-800 border-green-200'
                : 'bg-destructive/10 text-destructive border-destructive/20'
            }`}
          >
            {alert.type === 'success' ? (
              <CheckCircle2 className='h-4 w-4' />
            ) : (
              <AlertCircle className='h-4 w-4' />
            )}
            <AlertTitle>
              {alert.type === 'success' ? 'Success' : 'Error'}
            </AlertTitle>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}

        {step === 1 && (
          <>
            <div className='mb-6'>
              <Tabs
                defaultValue='monthly'
                onValueChange={value =>
                  handleCycleChange(value as BillingCycle)
                }
                className='w-full'
              >
                <div className='flex justify-center mb-6'>
                  <TabsList>
                    <TabsTrigger value='Monthly'>Monthly</TabsTrigger>
                    <TabsTrigger value='Annual'>Annual (Save 15%)</TabsTrigger>
                  </TabsList>
                </div>
              </Tabs>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-8'>
              {pricingInfo.map(pricing => (
                <SubscriptionCard
                  key={pricing.tier}
                  pricing={pricing}
                  selectedCycle={selectedCycle}
                  onSelect={() => handleSelectTier(pricing.tier)}
                  isSelected={selectedTier === pricing.tier}
                />
              ))}
            </div>

            <Card className='mb-8'>
              <CardHeader>
                <CardTitle>Discount Code</CardTitle>
                <CardDescription>
                  Enter a discount code if you have one.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='flex space-x-2'>
                  <Input
                    value={discountCode}
                    onChange={e => setDiscountCode(e.target.value)}
                    placeholder='Enter discount code'
                    disabled={discountApplied}
                  />
                  <Button
                    onClick={handleApplyDiscount}
                    disabled={discountApplied}
                    variant={discountApplied ? 'default' : 'outline'}
                    className={
                      discountApplied
                        ? 'bg-green-2010c hover:bg-green-2010c/90'
                        : ''
                    }
                  >
                    {discountApplied ? 'Applied' : 'Apply'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <div className='flex justify-end'>
              <Button
                onClick={handleContinueToPayment}
                disabled={!selectedTier}
              >
                Continue to Payment
              </Button>
            </div>
          </>
        )}

        {step === 2 && (
          <>
            <Card className='mb-6'>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
                <CardDescription>
                  Enter your payment details to complete your subscription.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PaymentMethodForm
                  onSubmit={handleSubmitPayment}
                  onCancel={() => setStep(1)}
                  isEditing={false}
                />
              </CardContent>
            </Card>
          </>
        )}

        {step === 3 && (
          <Card className='text-center py-8'>
            <CardContent>
              <div className='flex flex-col items-center justify-center space-y-4'>
                <div className='rounded-full bg-green-2010c/20 p-3'>
                  <CheckCircle2 className='h-12 w-12 text-green-2010c' />
                </div>
                <h2 className='text-2xl font-bold'>Subscription Successful!</h2>
                <p className='text-muted-foreground max-w-md mx-auto'>
                  Thank you for subscribing to Childfree Legacy. Your
                  subscription is now active, and you have full access to all
                  features.
                </p>
                <Button
                  onClick={handleFinish}
                  className='mt-4 bg-green-2010c hover:bg-green-2010c/90'
                >
                  Go to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
