'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { SubscriptionStatusCard } from '@/components/billing/subscription-status-card';
import { PaymentMethodCard } from '@/components/billing/payment-method-card';
import { PaymentMethodForm } from '@/components/billing/payment-method-form';
import { TransactionHistoryTable } from '@/components/billing/transaction-history-table';
import {
  AlertCircle,
  CheckCircle2,
  CreditCard,
  Download,
  Plus,
} from 'lucide-react';
import { Headline, Subhead } from '../../../components/ui/brand/typography';
import { mockUserBillingInfo } from '@/components/billing/mock-data';
import { UserBillingInfo, PaymentMethod } from '@/components/billing/types';

export default function BillingDashboardPage() {
  const router = useRouter();
  const [billingInfo, setBillingInfo] = useState<UserBillingInfo | null>(null);
  const [isAddPaymentModalOpen, setIsAddPaymentModalOpen] = useState(false);
  const [editingPaymentMethod, setEditingPaymentMethod] = useState<
    string | null
  >(null);
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  useEffect(() => {
    // In a real implementation, this would fetch data from an API
    // For now, we'll use mock data
    setBillingInfo(mockUserBillingInfo);
  }, []);

  const handleAddPaymentMethod = () => {
    setEditingPaymentMethod(null);
    setIsAddPaymentModalOpen(true);
  };

  const handleEditPaymentMethod = (id: string) => {
    setEditingPaymentMethod(id);
    setIsAddPaymentModalOpen(true);
  };

  const handleDeletePaymentMethod = (id: string) => {
    if (confirm('Are you sure you want to remove this payment method?')) {
      // In a real implementation, this would call an API to delete the payment method
      if (billingInfo) {
        const updatedPaymentMethods = billingInfo.paymentMethods.filter(
          pm => pm.id !== id
        );
        setBillingInfo({
          ...billingInfo,
          paymentMethods: updatedPaymentMethods,
        });

        setAlert({
          type: 'success',
          message: 'Payment method removed successfully.',
        });

        // Clear alert after 5 seconds
        setTimeout(() => setAlert(null), 5000);
      }
    }
  };

  const handleSetDefaultPaymentMethod = (id: string) => {
    // In a real implementation, this would call an API to set the default payment method
    if (billingInfo) {
      const updatedPaymentMethods = billingInfo.paymentMethods.map(pm => ({
        ...pm,
        isDefault: pm.id === id,
      }));

      setBillingInfo({
        ...billingInfo,
        paymentMethods: updatedPaymentMethods,
      });

      setAlert({
        type: 'success',
        message: 'Default payment method updated successfully.',
      });

      // Clear alert after 5 seconds
      setTimeout(() => setAlert(null), 5000);
    }
  };

  const handleSubmitPaymentMethod = (data: any) => {
    // In a real implementation, this would call an API to add or update the payment method
    if (billingInfo) {
      let updatedPaymentMethods: PaymentMethod[];

      if (editingPaymentMethod) {
        // Update existing payment method
        updatedPaymentMethods = billingInfo.paymentMethods.map(pm => {
          if (pm.id === editingPaymentMethod) {
            return {
              ...pm,
              type: data.type,
              lastFour:
                data.type === 'ACH'
                  ? data.accountNumber.slice(-4)
                  : data.cardNumber.slice(-4),
              expiryDate:
                data.type === 'ACH'
                  ? undefined
                  : `${data.expiryMonth}/${data.expiryYear.slice(-2)}`,
              isDefault: data.isDefault ? true : pm.isDefault,
              cardBrand: data.type === 'ACH' ? undefined : 'Visa', // Simplified for demo
              bankName: data.type === 'ACH' ? data.bankName : undefined,
            };
          }
          return data.isDefault ? { ...pm, isDefault: false } : pm;
        });

        setAlert({
          type: 'success',
          message: 'Payment method updated successfully.',
        });
      } else {
        // Add new payment method
        const newPaymentMethod: PaymentMethod = {
          id: `pm_${Date.now()}`,
          type: data.type,
          lastFour:
            data.type === 'ACH'
              ? data.accountNumber.slice(-4)
              : data.cardNumber.slice(-4),
          expiryDate:
            data.type === 'ACH'
              ? undefined
              : `${data.expiryMonth}/${data.expiryYear.slice(-2)}`,
          isDefault: data.isDefault,
          cardBrand: data.type === 'ACH' ? undefined : 'Visa', // Simplified for demo
          bankName: data.type === 'ACH' ? data.bankName : undefined,
        };

        updatedPaymentMethods = data.isDefault
          ? billingInfo.paymentMethods
              .map(pm => ({ ...pm, isDefault: false }))
              .concat(newPaymentMethod)
          : [...billingInfo.paymentMethods, newPaymentMethod];

        setAlert({
          type: 'success',
          message: 'Payment method added successfully.',
        });
      }

      setBillingInfo({
        ...billingInfo,
        paymentMethods: updatedPaymentMethods,
      });

      setIsAddPaymentModalOpen(false);
      setEditingPaymentMethod(null);

      // Clear alert after 5 seconds
      setTimeout(() => setAlert(null), 5000);
    }
  };

  const handleUpdatePayment = () => {
    handleAddPaymentMethod();
  };

  const handleCancelSubscription = () => {
    router.push('/dashboard/billing/cancel');
  };

  const handleResolveDelinquency = () => {
    router.push('/dashboard/billing/update-payment');
  };

  const handleViewReceipt = (transactionId: string) => {
    // In a real implementation, this would open a receipt viewer
    console.log(`Viewing receipt for transaction ${transactionId}`);
    window.alert(`Viewing receipt for transaction ${transactionId}`);
  };

  const handleDownloadReceipt = (transactionId: string) => {
    // In a real implementation, this would download a receipt
    console.log(`Downloading receipt for transaction ${transactionId}`);
    window.alert(`Downloading receipt for transaction ${transactionId}`);
  };

  if (!billingInfo) {
    return (
      <div className='container mx-auto py-8 px-4'>
        <div className='max-w-4xl mx-auto'>
          <div className='flex items-center justify-center h-64'>
            <p className='text-muted-foreground'>
              Loading billing information...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto py-8 px-4'>
      <div className='max-w-4xl mx-auto'>
        <div className='mb-8'>
          <Headline className='mb-2'>Billing & Subscription</Headline>
          <Subhead className='text-muted-foreground'>
            Manage your subscription, payment methods, and billing history.
          </Subhead>
        </div>

        {alert && (
          <Alert
            className={`mb-6 ${
              alert.type === 'success'
                ? 'bg-green-50 text-green-800 border-green-200'
                : 'bg-destructive/10 text-destructive border-destructive/20'
            }`}
          >
            {alert.type === 'success' ? (
              <CheckCircle2 className='h-4 w-4' />
            ) : (
              <AlertCircle className='h-4 w-4' />
            )}
            <AlertTitle>
              {alert.type === 'success' ? 'Success' : 'Error'}
            </AlertTitle>
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}

        <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
          <div className='md:col-span-2'>
            {billingInfo.subscription ? (
              <SubscriptionStatusCard
                subscription={billingInfo.subscription}
                onUpdatePayment={handleUpdatePayment}
                onCancelSubscription={handleCancelSubscription}
                onResolveDelinquency={
                  billingInfo.subscription.status === 'Delinquent' ||
                  billingInfo.subscription.status === 'Suspended'
                    ? handleResolveDelinquency
                    : undefined
                }
              />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Active Subscription</CardTitle>
                  <CardDescription>
                    You don't have an active subscription. Subscribe to access
                    all features.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={() => router.push('/dashboard/billing/subscribe')}
                  >
                    Subscribe Now
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className='space-y-2'>
                <Button
                  variant='outline'
                  className='w-full justify-start'
                  onClick={() => router.push('/dashboard/billing/history')}
                >
                  <Download className='mr-2 h-4 w-4' />
                  Download Invoices
                </Button>
                <Button
                  variant='outline'
                  className='w-full justify-start'
                  onClick={handleAddPaymentMethod}
                >
                  <CreditCard className='mr-2 h-4 w-4' />
                  Add Payment Method
                </Button>
                {billingInfo.subscription && (
                  <Button
                    variant='outline'
                    className='w-full justify-start'
                    onClick={() => router.push('/dashboard/billing/subscribe')}
                  >
                    <Plus className='mr-2 h-4 w-4' />
                    Change Plan
                  </Button>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        <Tabs defaultValue='payment-methods' className='mb-8'>
          <TabsList className='mb-4'>
            <TabsTrigger value='payment-methods'>Payment Methods</TabsTrigger>
            <TabsTrigger value='billing-history'>Billing History</TabsTrigger>
          </TabsList>

          <TabsContent value='payment-methods'>
            <div className='mb-4 flex justify-between items-center'>
              <h2 className='text-xl font-semibold'>Payment Methods</h2>
              <Button onClick={handleAddPaymentMethod}>
                <Plus className='mr-2 h-4 w-4' />
                Add Payment Method
              </Button>
            </div>

            {billingInfo.paymentMethods.length === 0 ? (
              <Card>
                <CardContent className='py-8 text-center'>
                  <p className='text-muted-foreground mb-4'>
                    No payment methods added yet.
                  </p>
                  <Button onClick={handleAddPaymentMethod}>
                    <Plus className='mr-2 h-4 w-4' />
                    Add Payment Method
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className='space-y-4'>
                {billingInfo.paymentMethods.map(paymentMethod => (
                  <PaymentMethodCard
                    key={paymentMethod.id}
                    paymentMethod={paymentMethod}
                    onEdit={handleEditPaymentMethod}
                    onDelete={handleDeletePaymentMethod}
                    onSetDefault={handleSetDefaultPaymentMethod}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value='billing-history'>
            <div className='mb-4 flex justify-between items-center'>
              <h2 className='text-xl font-semibold'>Billing History</h2>
              <Button
                variant='outline'
                onClick={() => router.push('/dashboard/billing/history')}
              >
                View All
              </Button>
            </div>

            <TransactionHistoryTable
              transactions={billingInfo.transactions}
              onViewReceipt={handleViewReceipt}
              onDownloadReceipt={handleDownloadReceipt}
            />
          </TabsContent>
        </Tabs>

        <Dialog
          open={isAddPaymentModalOpen}
          onOpenChange={setIsAddPaymentModalOpen}
        >
          <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
              <DialogTitle>
                {editingPaymentMethod
                  ? 'Edit Payment Method'
                  : 'Add Payment Method'}
              </DialogTitle>
              <DialogDescription>
                {editingPaymentMethod
                  ? 'Update your payment method details.'
                  : 'Add a new payment method to your account.'}
              </DialogDescription>
            </DialogHeader>

            <PaymentMethodForm
              onSubmit={handleSubmitPaymentMethod}
              onCancel={() => setIsAddPaymentModalOpen(false)}
              isEditing={!!editingPaymentMethod}
            />
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
