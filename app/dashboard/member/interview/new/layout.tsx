'use client';

import { FC, ReactNode } from 'react';
import { InterviewNewProvider } from '@/components/interview/interview-new-context';

type InterviewLayoutProps = {
  children: ReactNode;
};

const InterviewLayout: FC<InterviewLayoutProps> = ({ children }) => {
  return (
    <InterviewNewProvider defaultInterviewSelector='mostQuestions'>
      {children}
    </InterviewNewProvider>
  );
};

export default InterviewLayout;
