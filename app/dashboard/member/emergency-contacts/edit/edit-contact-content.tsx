'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ContactForm } from '@/components/emergency-contacts/ContactForm';
import { useEmergencyContacts } from '@/hooks/useEmergencyContacts';
import type { EmergencyContact } from '@/hooks/useEmergencyContacts';
import { toast } from 'sonner';
import { ContactFormSkeleton } from '@/components/ui/skeletons/contact-form-skeleton';

export function EditContactContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { updateContact, contacts, loading } = useEmergencyContacts();
  const [isLoading, setIsLoading] = useState(true);
  const [initialData, setInitialData] = useState<
    Omit<EmergencyContact, 'id'> | undefined
  >(undefined);
  const [contactId, setContactId] = useState<string | null>(null);

  // Get the contact data from the contacts list
  useEffect(() => {
    if (typeof window !== 'undefined' && !loading && contacts.length > 0) {
      try {
        // Get contact ID from URL
        const id = searchParams.get('id');

        if (!id) {
          toast.error('No contact selected for editing');
          router.push('/dashboard/member/emergency-contacts');
          return;
        }

        // Find the contact in the contacts list
        const contactToEdit = contacts.find(contact => contact.id === id);

        if (contactToEdit) {
          setInitialData({
            fullName: contactToEdit.fullName,
            relationship: contactToEdit.relationship,
            phoneNumber: contactToEdit.phoneNumber,
            emailAddress: contactToEdit.emailAddress,
            contactType: contactToEdit.contactType,
            isPrimaryForType: contactToEdit.isPrimaryForType || false,
          });
          setContactId(id);
          setIsLoading(false);
        } else {
          console.error('Contact not found', {
            id,
            availableContacts: contacts,
          });
          toast.error('Contact not found');
          router.push('/dashboard/member/emergency-contacts');
        }
      } catch (error) {
        console.error('Error loading contact data:', error);
        toast.error('Error loading contact data');
        router.push('/dashboard/member/emergency-contacts');
      }
    } else if (!loading && contacts.length === 0) {
      // If contacts are loaded but empty
      toast.error('No contacts available');
      router.push('/dashboard/member/emergency-contacts');
    }
    // Keep loading state true while contacts are being fetched
  }, [router, searchParams, contacts, loading]);

  const handleUpdateContact = async (
    formData: Omit<EmergencyContact, 'id'>
  ) => {
    if (!contactId) {
      throw new Error('No contact ID provided');
    }

    try {
      await updateContact(contactId, formData);
      return; // The form component will handle navigation and toast
    } catch (error) {
      console.error('Error updating contact:', error);
      throw error;
    }
  };

  return (
    <div className='max-w-7xl mx-auto'>
      {isLoading ? (
        <ContactFormSkeleton />
      ) : (
        <ContactForm
          initialData={initialData}
          onSubmit={handleUpdateContact}
          isEditing={true}
          isLoading={false}
        />
      )}
    </div>
  );
}
