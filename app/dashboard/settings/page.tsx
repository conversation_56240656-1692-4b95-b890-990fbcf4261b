'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { NotificationPreferences } from '@/components/notifications/notification-preferences';
import {
  User,
  Shield,
  Bell,
  Accessibility,
  HelpCircle,
  History,
  Globe,
  Monitor,
  LogOut,
  Save,
} from 'lucide-react';

import ChangePasswordDialog from '@/components/ChangePasswordDialog';
import { fetchDevices, signOut, forgetDevice } from 'aws-amplify/auth';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useAuth } from '@/app/context/AuthContext';
import { fetchLoginHistory } from '@/utils/loginHistory';
import {
  fetchUserProfileByCognitoId,
  updateUserProfile,
} from '@/lib/data/users';

interface AccessibilitySettings {
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  highContrast: boolean;
  screenReader: boolean;
  reducedMotion: boolean;
}

export default function SettingsPage() {
  const { refreshUser, user } = useAuth();
  const queryClient = useQueryClient();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [isSavingProfile, setIsSavingProfile] = useState(false);

  // AWS Cognito device tracking
  const { data: userDevices, isLoading: isLoadingDevices } = useQuery({
    queryKey: ['userDevices'],
    queryFn: fetchDevices,
  });

  // Fetch real user profile data
  const {
    data: userProfile,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile,
  } = useQuery({
    queryKey: ['userProfile', user?.userId],
    queryFn: () => fetchUserProfileByCognitoId(user?.userId || ''),
    enabled: !!user?.userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch real login history
  const {
    data: loginHistory,
    isLoading: isLoadingLoginHistory,
    error: loginHistoryError,
  } = useQuery({
    queryKey: ['loginHistory', user?.signInDetails?.loginId],
    queryFn: () => fetchLoginHistory(user?.signInDetails?.loginId || ''),
    enabled: !!user?.signInDetails?.loginId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Helper function to check if device is current
  const isCurrentDevice = (device: any) => {
    // Check if this device was used most recently or has current session indicators
    if (!userDevices || userDevices.length === 0) return false;

    // Find the device with the most recent lastAuthenticatedDate
    const mostRecentDevice = userDevices.reduce((latest: any, current: any) => {
      if (!latest.lastAuthenticatedDate) return current;
      if (!current.lastAuthenticatedDate) return latest;

      return new Date(current.lastAuthenticatedDate) >
        new Date(latest.lastAuthenticatedDate)
        ? current
        : latest;
    });

    return device.id === mostRecentDevice.id;
  };

  // Local state for form data (editable fields)
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    birthdate: '',
  });

  // Initialize form data when user profile loads
  useEffect(() => {
    if (userProfile) {
      setFormData({
        firstName: userProfile.firstName || '',
        lastName: userProfile.lastName || '',
        phoneNumber: userProfile.phoneNumber || '',
        birthdate: userProfile.birthdate || '',
      });
    }
  }, [userProfile]);

  // Handle saving profile changes
  const handleSaveProfile = async () => {
    if (!userProfile?.id) {
      toast.error('User profile not loaded');
      return;
    }

    // Validate required fields
    if (!formData.firstName.trim()) {
      toast.error('First name is required');
      return;
    }
    if (!formData.lastName.trim()) {
      toast.error('Last name is required');
      return;
    }
    if (!formData.phoneNumber.trim()) {
      toast.error('Phone number is required');
      return;
    }
    if (!formData.birthdate.trim()) {
      toast.error('Date of birth is required');
      return;
    }

    try {
      setIsSavingProfile(true);

      // Update user with new data (excluding state which is read-only)
      await updateUserProfile(userProfile.id, {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        phoneNumber: formData.phoneNumber.trim(),
        birthdate: formData.birthdate,
      } as any);

      // Refresh the profile data
      await refetchProfile();
      toast.success('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile. Please try again.');
    } finally {
      setIsSavingProfile(false);
    }
  };

  // Security settings (without mock login history)
  const [securitySettings, setSecuritySettings] = useState({
    mfaEnabled: false,
    rememberMe: false, // Will be initialized from localStorage
  });

  // Mock accessibility settings
  const [accessibilitySettings, setAccessibilitySettings] =
    useState<AccessibilitySettings>({
      fontSize: 'medium',
      highContrast: false,
      screenReader: false,
      reducedMotion: false,
    });

  // Initialize rememberMe from localStorage
  useEffect(() => {
    const storedRememberMe = localStorage.getItem('rememberMe');
    if (storedRememberMe !== null) {
      const rememberMeValue = storedRememberMe === 'true';
      setSecuritySettings(prev => ({
        ...prev,
        rememberMe: rememberMeValue,
      }));
    }
  }, []);

  // Handle remember me change
  const handleRememberMeChange = (checked: boolean) => {
    localStorage.setItem('rememberMe', checked.toString());
    setSecuritySettings(prev => ({
      ...prev,
      rememberMe: checked,
    }));
  };

  // Device management functions
  const handleForgetDevice = async (deviceId: string) => {
    try {
      console.log('===> Forgetting device', deviceId);
      await forgetDevice({ device: { id: deviceId } });
      toast.success('Device removed successfully');
      queryClient.invalidateQueries({ queryKey: ['userDevices'] });
    } catch (error) {
      console.error('Error forgetting device:', error);
      toast.error('Failed to remove device');
    }
  };

  const handleSignOutAllDevices = async () => {
    try {
      setIsSigningOut(true);
      await signOut({ global: true });
      toast.success('Successfully signed out from all devices');
      await refreshUser();
      queryClient.invalidateQueries({ queryKey: ['userDevices'] });
    } catch (error) {
      console.error('Error signing out from all devices:', error);
      toast.error('Failed to sign out from all devices');
    } finally {
      setIsSigningOut(false);
    }
  };

  return (
    <div className='container mx-auto p-4 py-8'>
      <div className='mb-8'>
        <h1 className='text-3xl font-geologica font-semibold'>Settings</h1>
        <p className='text-[var(--custom-gray-medium)] mt-2'>
          Manage your account preferences and settings
        </p>
      </div>

      <Tabs defaultValue='profile' className='w-full'>
        <TabsList className='grid w-full grid-cols-6 mb-6'>
          <TabsTrigger value='profile' className='cursor-pointer flex-1'>
            <User className='h-4 w-4 mr-2' />
            Profile
          </TabsTrigger>
          <TabsTrigger value='security' className='cursor-pointer flex-1'>
            <Shield className='h-4 w-4 mr-2' />
            Security
          </TabsTrigger>
          <div className='relative group flex-1'>
            <TabsTrigger
              value='notifications'
              disabled
              className='cursor-not-allowed opacity-50 w-full flex items-center justify-center'
            >
              <Bell className='h-4 w-4 mr-2' />
              Notifications
            </TabsTrigger>
            <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50'>
              In development
            </div>
          </div>
          <div className='relative group flex-1'>
            <TabsTrigger
              value='accessibility'
              disabled
              className='cursor-not-allowed opacity-50 w-full flex items-center justify-center'
            >
              <Accessibility className='h-4 w-4 mr-2' />
              Accessibility
            </TabsTrigger>
            <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50'>
              In development
            </div>
          </div>
          <div className='relative group flex-1'>
            <TabsTrigger
              value='support'
              disabled
              className='cursor-not-allowed opacity-50 w-full flex items-center justify-center'
            >
              <HelpCircle className='h-4 w-4 mr-2' />
              Support
            </TabsTrigger>
            <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50'>
              In development
            </div>
          </div>
          <TabsTrigger value='history' className='cursor-pointer flex-1'>
            <History className='h-4 w-4 mr-2' />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value='profile'>
          <div className='space-y-6'>
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <User className='h-5 w-5' />
                  <span>Basic Information</span>
                </CardTitle>
                <CardDescription>
                  Your personal information and account details
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {isLoadingProfile ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading profile information...
                    </p>
                  </div>
                ) : profileError ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-red-500'>
                      Failed to load profile information. Please try again
                      later.
                    </p>
                  </div>
                ) : (
                  <>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label htmlFor='firstName'>First Name *</Label>
                        <Input
                          id='firstName'
                          value={formData.firstName}
                          onChange={e =>
                            setFormData(prev => ({
                              ...prev,
                              firstName: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>
                      <div className='space-y-2'>
                        <Label htmlFor='lastName'>Last Name *</Label>
                        <Input
                          id='lastName'
                          value={formData.lastName}
                          onChange={e =>
                            setFormData(prev => ({
                              ...prev,
                              lastName: e.target.value,
                            }))
                          }
                          required
                        />
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='email'>Email Address</Label>
                      <Input
                        id='email'
                        type='email'
                        value={userProfile?.email || ''}
                        disabled
                        className='bg-gray-50'
                      />
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        Email cannot be changed. Contact support if you need to
                        update your email.
                      </p>
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='phone'>Phone Number *</Label>
                      <Input
                        id='phone'
                        type='tel'
                        value={formData.phoneNumber}
                        onChange={e =>
                          setFormData(prev => ({
                            ...prev,
                            phoneNumber: e.target.value,
                          }))
                        }
                        placeholder='Enter your phone number'
                        required
                      />
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='dateOfBirth'>Date of Birth *</Label>
                      <Input
                        id='dateOfBirth'
                        type='date'
                        value={formData.birthdate}
                        onChange={e =>
                          setFormData(prev => ({
                            ...prev,
                            birthdate: e.target.value,
                          }))
                        }
                        required
                      />
                    </div>

                    <div className='space-y-2'>
                      <Label htmlFor='state'>State</Label>
                      <Input
                        id='state'
                        value={userProfile?.state || ''}
                        disabled
                        className='bg-gray-50'
                      />
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        State cannot be changed. Contact support if you need to
                        update your state.
                      </p>
                    </div>

                    <div className='pt-4'>
                      <Button
                        onClick={handleSaveProfile}
                        disabled={isSavingProfile}
                        className='w-full'
                      >
                        <Save className='h-4 w-4 mr-2' />
                        {isSavingProfile ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Account Status */}
            <Card>
              <CardHeader>
                <CardTitle>Account Status</CardTitle>
                <CardDescription>
                  Your current role and account information
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {isLoadingProfile ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading account information...
                    </p>
                  </div>
                ) : userProfile ? (
                  <>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      <div className='space-y-2'>
                        <Label>Current Role</Label>
                        <div className='flex items-center space-x-2'>
                          <Badge variant='default'>{userProfile.role}</Badge>
                          {userProfile.subrole && (
                            <Badge variant='secondary'>
                              {userProfile.subrole}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className='space-y-2'>
                        <Label>Account Status</Label>
                        <Badge
                          variant={
                            userProfile.status === 'active'
                              ? 'default'
                              : 'destructive'
                          }
                        >
                          {userProfile.status.charAt(0).toUpperCase() +
                            userProfile.status.slice(1)}
                        </Badge>
                      </div>
                    </div>

                    <div className='space-y-2'>
                      <Label>User ID</Label>
                      <Input
                        value={userProfile.id}
                        disabled
                        className='bg-gray-50'
                      />
                    </div>

                    {userProfile.journeyStatus && (
                      <div className='space-y-2'>
                        <Label>Journey Status</Label>
                        <Badge variant='outline'>
                          {userProfile.journeyStatus}
                        </Badge>
                      </div>
                    )}
                  </>
                ) : (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-red-500'>
                      Failed to load account information.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Optional Services */}
            {/* <Card>
              <CardHeader>
                <CardTitle>Optional Services</CardTitle>
                <CardDescription>
                  Manage your subscription to additional services
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Newsletter Subscription</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Receive our monthly newsletter with updates
                    </p>
                  </div>
                  <Switch
                    checked={true}
                    onCheckedChange={checked => {
                      // TODO: Implement optional services functionality
                      console.log('Newsletter subscription:', checked);
                    }}
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Legal Updates</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Get notified about legal changes affecting your documents
                    </p>
                  </div>
                  <Switch
                    checked={true}
                    onCheckedChange={checked => {
                      // TODO: Implement optional services functionality
                      console.log('Legal updates subscription:', checked);
                    }}
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Educational Materials</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Access to educational content and resources
                    </p>
                  </div>
                  <Switch
                    checked={false}
                    onCheckedChange={checked => {
                      // TODO: Implement optional services functionality
                      console.log(
                        'Educational materials subscription:',
                        checked
                      );
                    }}
                  />
                </div>
              </CardContent>
            </Card> */}
          </div>
        </TabsContent>

        <TabsContent value='security'>
          <div className='space-y-6'>
            {/* Password Settings */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <Shield className='h-5 w-5' />
                  <span>Password & Authentication</span>
                </CardTitle>
                <CardDescription>
                  Manage your password and two-factor authentication
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-4'>
                  <div className='space-y-2'>
                    <Label>Password Management</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Regularly update your password to keep your account
                      secure.
                    </p>
                    <ChangePasswordDialog
                      trigger={
                        <Button className='w-full'>Change Password</Button>
                      }
                    />
                  </div>
                </div>

                <div className='border-t pt-4'>
                  <div className='flex items-center justify-between'>
                    <div className='space-y-0.5'>
                      <Label>Two-Factor Authentication</Label>
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <Switch
                      checked={securitySettings.mfaEnabled}
                      onCheckedChange={checked =>
                        setSecuritySettings(prev => ({
                          ...prev,
                          mfaEnabled: checked,
                        }))
                      }
                    />
                  </div>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Remember Me</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Stay logged in for extended periods
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.rememberMe}
                    onCheckedChange={handleRememberMeChange}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Active Sessions */}
            <Card>
              <CardHeader>
                <CardTitle>Active Sessions</CardTitle>
                <CardDescription>
                  View and manage your active login sessions across different
                  devices and browsers
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {isLoadingDevices ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading device information...
                    </p>
                  </div>
                ) : !userDevices || userDevices.length === 0 ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      No devices found.
                    </p>
                  </div>
                ) : (
                  userDevices.map((device: any, index) => (
                    <div
                      key={device.id || index}
                      className='flex items-center justify-between p-4 border rounded-lg'
                    >
                      <div className='flex items-center space-x-3'>
                        <Monitor className='h-5 w-5 text-[var(--custom-gray-medium)]' />
                        <div>
                          <div className='flex flex-col'>
                            <p className='font-medium'>
                              {device.name
                                ? `Device ${device.name.slice(0, 10)}`
                                : 'Unknown Device'}
                            </p>
                            {isCurrentDevice(device) && (
                              <span className='text-xs text-green-600 font-medium'>
                                Current Session
                              </span>
                            )}
                          </div>
                          <p className='text-sm text-[var(--custom-gray-medium)]'>
                            Created:{' '}
                            {device.createDate
                              ? format(
                                  new Date(device.createDate),
                                  'MMM d, yyyy'
                                )
                              : 'Unknown'}
                          </p>
                          <p className='text-xs text-[var(--custom-gray-medium)]'>
                            Last used:{' '}
                            {device.lastAuthenticatedDate
                              ? format(
                                  new Date(device.lastAuthenticatedDate),
                                  'MMM d, yyyy h:mm a'
                                )
                              : 'Unknown'}
                          </p>
                        </div>
                      </div>
                      <div className='flex items-center space-x-2'>
                        {isCurrentDevice(device) && (
                          <Badge variant='default'>Current</Badge>
                        )}
                        {!isCurrentDevice(device) && (
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleForgetDevice(device.id)}
                            disabled={isSigningOut}
                          >
                            <LogOut className='h-4 w-4 mr-1' />
                            Remove Device
                          </Button>
                        )}
                      </div>
                    </div>
                  ))
                )}

                <Button
                  variant='outline'
                  className='w-full'
                  onClick={handleSignOutAllDevices}
                  disabled={isSigningOut}
                >
                  <LogOut className='h-4 w-4 mr-2' />
                  {isSigningOut ? 'Signing out...' : 'Sign Out All Devices'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='notifications'>
          <NotificationPreferences />
        </TabsContent>

        <TabsContent value='accessibility'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Accessibility className='h-5 w-5' />
                <span>Accessibility Settings</span>
              </CardTitle>
              <CardDescription>
                Customize the interface for better accessibility
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label>Font Size</Label>
                  <div className='grid grid-cols-4 gap-2'>
                    {(['small', 'medium', 'large', 'extra-large'] as const).map(
                      size => (
                        <Button
                          key={size}
                          variant={
                            accessibilitySettings.fontSize === size
                              ? 'default'
                              : 'outline'
                          }
                          size='sm'
                          onClick={() =>
                            setAccessibilitySettings(prev => ({
                              ...prev,
                              fontSize: size,
                            }))
                          }
                          className='capitalize'
                        >
                          {size === 'extra-large' ? 'XL' : size}
                        </Button>
                      )
                    )}
                  </div>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>High Contrast Mode</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Increase contrast for better visibility
                    </p>
                  </div>
                  <Switch
                    checked={accessibilitySettings.highContrast}
                    onCheckedChange={checked =>
                      setAccessibilitySettings(prev => ({
                        ...prev,
                        highContrast: checked,
                      }))
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Screen Reader Support</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Optimize for screen reading software
                    </p>
                  </div>
                  <Switch
                    checked={accessibilitySettings.screenReader}
                    onCheckedChange={checked =>
                      setAccessibilitySettings(prev => ({
                        ...prev,
                        screenReader: checked,
                      }))
                    }
                  />
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Reduced Motion</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Minimize animations and transitions
                    </p>
                  </div>
                  <Switch
                    checked={accessibilitySettings.reducedMotion}
                    onCheckedChange={checked =>
                      setAccessibilitySettings(prev => ({
                        ...prev,
                        reducedMotion: checked,
                      }))
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='support'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <HelpCircle className='h-5 w-5' />
                <span>Support & Feedback</span>
              </CardTitle>
              <CardDescription>
                Get help or provide feedback about your experience
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <Button variant='outline' className='h-20 flex-col'>
                  <HelpCircle className='h-6 w-6 mb-2' />
                  <span>Contact Support</span>
                </Button>
                <Button variant='outline' className='h-20 flex-col'>
                  <Globe className='h-6 w-6 mb-2' />
                  <span>Knowledge Base</span>
                </Button>
              </div>

              <div className='space-y-4'>
                <Label htmlFor='feedback'>Send Feedback</Label>
                <textarea
                  id='feedback'
                  className='w-full min-h-[100px] p-3 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                  placeholder='Tell us about your experience or report an issue...'
                />
                <Button className='w-full'>Send Feedback</Button>
              </div>

              <div className='bg-gray-50 p-4 rounded-lg'>
                <h4 className='font-medium mb-2'>Quick Help</h4>
                <ul className='space-y-1 text-sm text-[var(--custom-gray-medium)]'>
                  <li>• Email: <EMAIL></li>
                  <li>• Phone: 1-800-LEGACY-1</li>
                  <li>• Hours: Monday-Friday, 9 AM - 6 PM EST</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='history'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <History className='h-5 w-5' />
                <span>Account History</span>
              </CardTitle>
              <CardDescription>
                View your account activity and audit trail
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <h4 className='font-medium'>Login History</h4>
                  <Badge variant='outline' className='text-xs'>
                    Successful Logins
                  </Badge>
                </div>
                {isLoadingLoginHistory ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading login history...
                    </p>
                  </div>
                ) : loginHistoryError ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-red-500'>
                      Failed to load login history. Please try again later.
                    </p>
                  </div>
                ) : !loginHistory || loginHistory.length === 0 ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      No login history found.
                    </p>
                  </div>
                ) : (
                  loginHistory.map((login, index) => (
                    <div
                      key={login.id}
                      className={`flex items-center justify-between p-3 border rounded-lg ${
                        index === 0
                          ? ' border-green-500'
                          : ' border-[var(--custom-gray-light)]'
                      }`}
                    >
                      <div className='flex items-center space-x-3'>
                        <div
                          className={`h-2 w-2 rounded-full ${
                            index === 0
                              ? 'bg-green-500'
                              : 'bg-[var(--custom-gray-medium)]'
                          }`}
                        />
                        <div>
                          <p className='font-medium'>{login.device}</p>
                          <p className='text-sm text-[var(--custom-gray-medium)]'>
                            {login.location} Device
                          </p>
                          <p className='text-xs text-[var(--foreground)] font-medium'>
                            {index === 0
                              ? 'Most recent login'
                              : 'Successful login'}
                          </p>
                        </div>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          {format(new Date(login.timestamp), 'MMM d, yyyy')}
                        </p>
                        <p className='text-xs text-[var(--custom-gray-medium)]'>
                          {format(new Date(login.timestamp), 'h:mm a')}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className='border-t pt-4 space-y-2'>
                <Button
                  variant='outline'
                  className='w-full'
                  onClick={() =>
                    queryClient.invalidateQueries({
                      queryKey: ['loginHistory'],
                    })
                  }
                  disabled={isLoadingLoginHistory}
                >
                  <History className='h-4 w-4 mr-2' />
                  {isLoadingLoginHistory
                    ? 'Refreshing...'
                    : 'Refresh Login History'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
