'use client';

import React from 'react';
import { Sidebar } from '@/components/dashboard/sidebar';
import { AdminContainer } from '../../components/ui/container';

export default function LinkedAccountLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className='flex bg-background min-h-[calc(100vh-4rem)]'>
      <Sidebar userRole='Member' />
      <div className='flex-1'>
        <main>
          <AdminContainer>{children}</AdminContainer>
        </main>
      </div>
    </div>
  );
}
