import { Schema } from '@/amplify/data/resource';

// Use auto-generated Document type from Amplify schema
export type Document = Schema['Document']['type'];

export interface DocumentReviewData {
  documentId: string;
  reviewerId: string;
  reviewDate: string;
  comments?: string;
  approved: boolean;
}

export interface AttorneyReview {
  id: string;
  documentId: string;
  attorneyId: string;
  attorneyName: string;
  attorneyPhone: string;
  attorneyEmail: string;
  status: 'requested' | 'in_progress' | 'completed';
  requestDate: string;
  completionDate?: string;
  comments?: string;
}

export interface DocumentStatus {
  id: string;
  documentId: string;
  memberId: string;
  status: Document['status'];
  trackingLink?: string;
  receiptDate?: string;
  approvalDate?: string;
  updatedAt: string;
  statusMessage?: string;
}

export interface SigningPackage {
  id: string;
  documentId: string;
  packageUrl: string;
  instructions: string;
  shippingLabel: string;
  generatedAt: string;
  expiresAt: string;
}
