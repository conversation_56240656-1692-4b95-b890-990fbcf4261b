'use client';

import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Search, X, Users, ChevronDown } from 'lucide-react';
import { User as UserType } from '@/types/account';

interface UserSelectorProps {
  selectedUser: UserType | null;
  onUserSelect: (user: UserType | null) => void;
  users: UserType[];
  className?: string;
}

export function UserSelector({
  selectedUser,
  onUserSelect,
  users,
  className = '',
}: UserSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  // Filter users based on search query
  const filteredUsers = useMemo(() => {
    if (!searchQuery.trim()) return users;

    const query = searchQuery.toLowerCase();
    return users.filter(
      user =>
        user.name.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        user.id.toLowerCase().includes(query)
    );
  }, [users, searchQuery]);

  // Group users by status for better organization
  const groupedUsers = useMemo(() => {
    // const active = filteredUsers.filter(user => user.status === 'active');
    const active = filteredUsers;
    const inactive = filteredUsers.filter(user => user.status === 'inactive');
    const pending = filteredUsers.filter(user => user.status === 'pending');

    return { active, inactive, pending };
  }, [filteredUsers]);

  const handleUserSelect = (user: UserType) => {
    onUserSelect(user);
    setIsOpen(false);
    setSearchQuery('');
  };

  const handleClearSelection = () => {
    onUserSelect(null);
  };

  return (
    <div className={`w-full ${className}`}>
      <Label className='text-sm font-medium mb-2 block'>
        Select Member to Manage
      </Label>

      <div className='flex gap-2'>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              className='flex-1 justify-between h-10'
              onClick={() => setIsOpen(!isOpen)}
            >
              {selectedUser ? (
                <div className='flex items-center gap-2 truncate'>
                  <User className='h-4 w-4 text-muted-foreground' />
                  <span className='truncate'>{selectedUser.name}</span>
                  <Badge variant='secondary' className='ml-auto'>
                    {selectedUser.status}
                  </Badge>
                </div>
              ) : (
                <div className='flex items-center gap-2 text-muted-foreground'>
                  <Users className='h-4 w-4' />
                  <span>Select a member...</span>
                </div>
              )}
              <ChevronDown className='h-4 w-4 opacity-50' />
            </Button>
          </PopoverTrigger>

          <PopoverContent className='w-[400px] p-0' align='start'>
            <Card className='border-0 shadow-none'>
              <CardHeader className='pb-3'>
                <CardTitle className='text-sm'>Select Member</CardTitle>
                <div className='relative'>
                  <Search className='absolute left-2 top-2.5 h-4 w-4 text-muted-foreground' />
                  <Input
                    placeholder='Search by name, email, or ID...'
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className='pl-8'
                  />
                </div>
              </CardHeader>

              <CardContent className='p-0 max-h-80 overflow-y-auto'>
                {filteredUsers.length === 0 ? (
                  <div className='p-4 text-center text-muted-foreground'>
                    <Users className='h-8 w-8 mx-auto mb-2 opacity-50' />
                    <p>No members found</p>
                  </div>
                ) : (
                  <div className='space-y-1'>
                    {/* Active Users */}
                    {groupedUsers.active.length > 0 && (
                      <div>
                        <div className='px-3 py-2 text-xs font-medium text-muted-foreground bg-muted/50'>
                          Active Members ({groupedUsers.active.length})
                        </div>
                        {groupedUsers.active.map(user => (
                          <div
                            key={user.id}
                            className='px-3 py-2 hover:bg-muted cursor-pointer border-b border-border/50'
                            onClick={() => handleUserSelect(user)}
                          >
                            <div className='flex items-center justify-between'>
                              <div className='flex-1 min-w-0'>
                                <p className='font-medium truncate'>
                                  {user.name}
                                </p>
                                <p className='text-sm text-muted-foreground truncate'>
                                  {user.email}
                                </p>
                                <p className='text-xs text-muted-foreground'>
                                  ID: {user.id}
                                </p>
                              </div>
                              <div className='flex items-center gap-2 ml-2'>
                                <Badge variant='outline' className='text-xs'>
                                  {user.role}
                                </Badge>
                                <Badge
                                  variant='default'
                                  className='text-xs bg-green-100 text-green-800'
                                >
                                  Active
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Pending Users */}
                    {groupedUsers.pending.length > 0 && (
                      <div>
                        <div className='px-3 py-2 text-xs font-medium text-muted-foreground bg-muted/50'>
                          Pending Members ({groupedUsers.pending.length})
                        </div>
                        {groupedUsers.pending.map(user => (
                          <div
                            key={user.id}
                            className='px-3 py-2 hover:bg-muted cursor-pointer border-b border-border/50'
                            onClick={() => handleUserSelect(user)}
                          >
                            <div className='flex items-center justify-between'>
                              <div className='flex-1 min-w-0'>
                                <p className='font-medium truncate'>
                                  {user.name}
                                </p>
                                <p className='text-sm text-muted-foreground truncate'>
                                  {user.email}
                                </p>
                                <p className='text-xs text-muted-foreground'>
                                  ID: {user.id}
                                </p>
                              </div>
                              <div className='flex items-center gap-2 ml-2'>
                                <Badge variant='outline' className='text-xs'>
                                  {user.role}
                                </Badge>
                                <Badge
                                  variant='outline'
                                  className='text-xs border-orange-300 text-orange-700'
                                >
                                  Pending
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Inactive Users */}
                    {groupedUsers.inactive.length > 0 && (
                      <div>
                        <div className='px-3 py-2 text-xs font-medium text-muted-foreground bg-muted/50'>
                          Inactive Members ({groupedUsers.inactive.length})
                        </div>
                        {groupedUsers.inactive.map(user => (
                          <div
                            key={user.id}
                            className='px-3 py-2 hover:bg-muted cursor-pointer border-b border-border/50 opacity-75'
                            onClick={() => handleUserSelect(user)}
                          >
                            <div className='flex items-center justify-between'>
                              <div className='flex-1 min-w-0'>
                                <p className='font-medium truncate'>
                                  {user.name}
                                </p>
                                <p className='text-sm text-muted-foreground truncate'>
                                  {user.email}
                                </p>
                                <p className='text-xs text-muted-foreground'>
                                  ID: {user.id}
                                </p>
                              </div>
                              <div className='flex items-center gap-2 ml-2'>
                                <Badge variant='outline' className='text-xs'>
                                  {user.role}
                                </Badge>
                                <Badge
                                  variant='outline'
                                  className='text-xs border-gray-300 text-[var(--custom-gray-medium)]'
                                >
                                  Inactive
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </PopoverContent>
        </Popover>

        {selectedUser && (
          <Button
            variant='outline'
            size='icon'
            onClick={handleClearSelection}
            className='h-10 w-10 shrink-0'
            title='Clear selection'
          >
            <X className='h-4 w-4' />
          </Button>
        )}
      </div>

      {selectedUser && (
        <div className='mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <User className='h-4 w-4 text-blue-600' />
              <span className='text-sm font-medium text-blue-800'>
                Managing: {selectedUser.name}
              </span>
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='outline' className='text-xs'>
                {selectedUser.role}
              </Badge>
              <Badge
                variant={
                  selectedUser.status === 'active' ? 'default' : 'outline'
                }
                className='text-xs'
              >
                {selectedUser.status}
              </Badge>
            </div>
          </div>
          <p className='text-xs text-blue-600 mt-1'>
            {selectedUser.email} • ID: {selectedUser.id}
          </p>
        </div>
      )}
    </div>
  );
}
