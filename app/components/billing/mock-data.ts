import {
  SubscriptionTier,
  BillingCycle,
  SubscriptionStatus,
  TransactionStatus,
  PaymentMethod,
  Subscription,
  Transaction,
  UserBillingInfo,
  AdminBillingStats,
  PricingInfo,
} from './types';

// Mock Payment Methods
export const mockPaymentMethods: PaymentMethod[] = [
  {
    id: 'pm_1',
    type: 'Credit Card',
    lastFour: '4242',
    expiryDate: '12/25',
    isDefault: true,
    cardBrand: 'Visa',
  },
  {
    id: 'pm_2',
    type: 'Debit Card',
    lastFour: '5678',
    expiryDate: '09/24',
    isDefault: false,
    cardBrand: 'MasterCard',
  },
  {
    id: 'pm_3',
    type: 'ACH',
    lastFour: '9876',
    isDefault: false,
    bankName: 'Chase Bank',
  },
];

// Mock Subscription
export const mockSubscription: Subscription = {
  id: 'sub_1',
  userId: 'user_1',
  tier: 'Basic',
  billingCycle: 'Monthly',
  status: 'Active',
  startDate: '2023-01-15',
  nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)
    .toISOString()
    .split('T')[0], // 15 days from now
  amount: 19.99,
  paymentMethodId: 'pm_1',
  discount: {
    type: 'Percentage',
    value: 10,
    code: 'WELCOME10',
  },
};

// Mock Delinquent Subscription
export const mockDelinquentSubscription: Subscription = {
  id: 'sub_2',
  userId: 'user_2',
  tier: 'Basic',
  billingCycle: 'Monthly',
  status: 'Delinquent',
  startDate: '2023-02-10',
  nextBillingDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
    .toISOString()
    .split('T')[0], // 5 days ago
  amount: 19.99,
  paymentMethodId: 'pm_2',
};

// Mock Canceled Subscription
export const mockCanceledSubscription: Subscription = {
  id: 'sub_3',
  userId: 'user_3',
  tier: 'Basic',
  billingCycle: 'Annual',
  status: 'Canceled',
  startDate: '2023-03-05',
  endDate: '2023-11-20',
  nextBillingDate: '2024-03-05',
  amount: 199.99,
  paymentMethodId: 'pm_1',
  cancelReason: 'Found a different service',
};

// Mock Transactions
export const mockTransactions: Transaction[] = [
  {
    id: 'tx_1',
    userId: 'user_1',
    subscriptionId: 'sub_1',
    date: '2023-11-15',
    amount: 19.99,
    status: 'Successful',
    paymentMethodId: 'pm_1',
    description: 'Monthly subscription payment',
    receiptUrl: '/receipts/tx_1.pdf',
  },
  {
    id: 'tx_2',
    userId: 'user_1',
    subscriptionId: 'sub_1',
    date: '2023-10-15',
    amount: 19.99,
    status: 'Successful',
    paymentMethodId: 'pm_1',
    description: 'Monthly subscription payment',
    receiptUrl: '/receipts/tx_2.pdf',
  },
  {
    id: 'tx_3',
    userId: 'user_1',
    subscriptionId: 'sub_1',
    date: '2023-09-15',
    amount: 19.99,
    status: 'Successful',
    paymentMethodId: 'pm_1',
    description: 'Monthly subscription payment',
    receiptUrl: '/receipts/tx_3.pdf',
  },
  {
    id: 'tx_4',
    userId: 'user_2',
    subscriptionId: 'sub_2',
    date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split('T')[0], // 5 days ago
    amount: 19.99,
    status: 'Failed',
    paymentMethodId: 'pm_2',
    description: 'Monthly subscription payment',
    failureReason: 'Insufficient funds',
    retryCount: 1,
  },
  {
    id: 'tx_5',
    userId: 'user_3',
    subscriptionId: 'sub_3',
    date: '2023-03-05',
    amount: 199.99,
    status: 'Successful',
    paymentMethodId: 'pm_1',
    description: 'Annual subscription payment',
    receiptUrl: '/receipts/tx_5.pdf',
  },
];

// Mock User Billing Info
export const mockUserBillingInfo: UserBillingInfo = {
  userId: 'user_1',
  subscription: mockSubscription,
  paymentMethods: mockPaymentMethods,
  transactions: mockTransactions.filter(tx => tx.userId === 'user_1'),
  billingAddress: {
    street: '123 Main St',
    city: 'San Francisco',
    state: 'CA',
    zipCode: '94105',
    country: 'USA',
  },
};

// Mock Admin Billing Stats
export const mockAdminBillingStats: AdminBillingStats = {
  totalActiveSubscriptions: 245,
  totalRevenue: 52487.5,
  revenueThisMonth: 4895.75,
  delinquentAccounts: 12,
  newSubscriptionsThisMonth: 28,
  canceledSubscriptionsThisMonth: 5,
};

// Mock Pricing Information
export const mockPricingInfo: PricingInfo[] = [
  {
    tier: 'Basic',
    monthlyPrice: 19.99,
    annualPrice: 199.99,
    features: [
      'Access to core estate planning tools',
      'Document generation and storage',
      'Emergency contact management',
      "Dead Man's Switch functionality",
      'Basic support',
    ],
    isPopular: true,
  },
  {
    tier: 'Enterprise',
    monthlyPrice: 49.99,
    annualPrice: 499.99,
    features: [
      'All Basic features',
      'Priority support',
      'Advanced document customization',
      'Multiple user accounts',
      'API access',
      'Custom branding',
    ],
  },
];

// Mock User Subscriptions for Admin View
export const mockUserSubscriptions: (Subscription & {
  userName: string;
  userEmail: string;
})[] = [
  {
    ...mockSubscription,
    userName: 'John Doe',
    userEmail: '<EMAIL>',
  },
  {
    ...mockDelinquentSubscription,
    userName: 'Jane Smith',
    userEmail: '<EMAIL>',
  },
  {
    ...mockCanceledSubscription,
    userName: 'Bob Johnson',
    userEmail: '<EMAIL>',
  },
  {
    id: 'sub_4',
    userId: 'user_4',
    tier: 'Basic',
    billingCycle: 'Monthly',
    status: 'Active',
    startDate: '2023-05-20',
    nextBillingDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split('T')[0], // 10 days from now
    amount: 19.99,
    paymentMethodId: 'pm_1',
    userName: 'Alice Williams',
    userEmail: '<EMAIL>',
  },
  {
    id: 'sub_5',
    userId: 'user_5',
    tier: 'Enterprise',
    billingCycle: 'Annual',
    status: 'Active',
    startDate: '2023-06-15',
    nextBillingDate: '2024-06-15',
    amount: 499.99,
    paymentMethodId: 'pm_3',
    userName: 'Charlie Brown',
    userEmail: '<EMAIL>',
  },
];
