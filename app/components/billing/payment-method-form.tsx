'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { PaymentMethodType } from './types';

interface PaymentMethodFormProps {
  onSubmit: (data: {
    type: PaymentMethodType;
    cardNumber?: string;
    expiryMonth?: string;
    expiryYear?: string;
    cvv?: string;
    cardholderName?: string;
    bankName?: string;
    accountNumber?: string;
    routingNumber?: string;
    isDefault: boolean;
  }) => void;
  onCancel: () => void;
  isEditing?: boolean;
}

export function PaymentMethodForm({
  onSubmit,
  onCancel,
  isEditing = false,
}: PaymentMethodFormProps) {
  const [paymentType, setPaymentType] =
    useState<PaymentMethodType>('Credit Card');
  const [cardNumber, setCardNumber] = useState('');
  const [expiryMonth, setExpiryMonth] = useState('');
  const [expiryYear, setExpiryYear] = useState('');
  const [cvv, setCvv] = useState('');
  const [cardholderName, setCardholderName] = useState('');
  const [bankName, setBankName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [routingNumber, setRoutingNumber] = useState('');
  const [isDefault, setIsDefault] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (paymentType === 'Credit Card' || paymentType === 'Debit Card') {
      if (!cardNumber.trim()) {
        newErrors.cardNumber = 'Card number is required';
      } else if (!/^\d{16}$/.test(cardNumber.replace(/\s/g, ''))) {
        newErrors.cardNumber = 'Please enter a valid 16-digit card number';
      }

      if (!expiryMonth) {
        newErrors.expiryMonth = 'Expiry month is required';
      }

      if (!expiryYear) {
        newErrors.expiryYear = 'Expiry year is required';
      }

      if (!cvv.trim()) {
        newErrors.cvv = 'CVV is required';
      } else if (!/^\d{3,4}$/.test(cvv)) {
        newErrors.cvv = 'Please enter a valid CVV';
      }

      if (!cardholderName.trim()) {
        newErrors.cardholderName = 'Cardholder name is required';
      }
    } else if (paymentType === 'ACH') {
      if (!bankName.trim()) {
        newErrors.bankName = 'Bank name is required';
      }

      if (!accountNumber.trim()) {
        newErrors.accountNumber = 'Account number is required';
      }

      if (!routingNumber.trim()) {
        newErrors.routingNumber = 'Routing number is required';
      } else if (!/^\d{9}$/.test(routingNumber)) {
        newErrors.routingNumber = 'Please enter a valid 9-digit routing number';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit({
        type: paymentType,
        cardNumber: paymentType === 'ACH' ? undefined : cardNumber,
        expiryMonth: paymentType === 'ACH' ? undefined : expiryMonth,
        expiryYear: paymentType === 'ACH' ? undefined : expiryYear,
        cvv: paymentType === 'ACH' ? undefined : cvv,
        cardholderName: paymentType === 'ACH' ? undefined : cardholderName,
        bankName: paymentType !== 'ACH' ? undefined : bankName,
        accountNumber: paymentType !== 'ACH' ? undefined : accountNumber,
        routingNumber: paymentType !== 'ACH' ? undefined : routingNumber,
        isDefault,
      });
    }
  };

  // Generate year options for expiry date
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 10 }, (_, i) => currentYear + i);

  return (
    <form onSubmit={handleSubmit} className='space-y-6'>
      <div className='space-y-4'>
        <div>
          <Label>Payment Method Type</Label>
          <RadioGroup
            value={paymentType}
            onValueChange={value => setPaymentType(value as PaymentMethodType)}
            className='flex flex-col space-y-2 mt-2'
          >
            <div className='flex items-center space-x-2'>
              <RadioGroupItem value='Credit Card' id='credit-card' />
              <Label htmlFor='credit-card' className='font-normal'>
                Credit Card
              </Label>
            </div>
            <div className='flex items-center space-x-2'>
              <RadioGroupItem value='Debit Card' id='debit-card' />
              <Label htmlFor='debit-card' className='font-normal'>
                Debit Card
              </Label>
            </div>
            <div className='flex items-center space-x-2'>
              <RadioGroupItem value='ACH' id='ach' />
              <Label htmlFor='ach' className='font-normal'>
                ACH Bank Transfer
              </Label>
            </div>
          </RadioGroup>
        </div>

        {(paymentType === 'Credit Card' || paymentType === 'Debit Card') && (
          <>
            <div className='space-y-2'>
              <Label htmlFor='cardNumber'>Card Number</Label>
              <Input
                id='cardNumber'
                value={cardNumber}
                onChange={e => setCardNumber(e.target.value)}
                placeholder='1234 5678 9012 3456'
                className={errors.cardNumber ? 'border-destructive' : ''}
              />
              {errors.cardNumber && (
                <p className='text-sm text-destructive'>{errors.cardNumber}</p>
              )}
            </div>

            <div className='grid grid-cols-3 gap-4'>
              <div className='space-y-2'>
                <Label htmlFor='expiryMonth'>Expiry Month</Label>
                <Select value={expiryMonth} onValueChange={setExpiryMonth}>
                  <SelectTrigger
                    id='expiryMonth'
                    className={errors.expiryMonth ? 'border-destructive' : ''}
                  >
                    <SelectValue placeholder='Month' />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 12 }, (_, i) => {
                      const month = i + 1;
                      return (
                        <SelectItem
                          key={month}
                          value={month.toString().padStart(2, '0')}
                        >
                          {month.toString().padStart(2, '0')}
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                {errors.expiryMonth && (
                  <p className='text-sm text-destructive'>
                    {errors.expiryMonth}
                  </p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='expiryYear'>Expiry Year</Label>
                <Select value={expiryYear} onValueChange={setExpiryYear}>
                  <SelectTrigger
                    id='expiryYear'
                    className={errors.expiryYear ? 'border-destructive' : ''}
                  >
                    <SelectValue placeholder='Year' />
                  </SelectTrigger>
                  <SelectContent>
                    {yearOptions.map(year => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.expiryYear && (
                  <p className='text-sm text-destructive'>
                    {errors.expiryYear}
                  </p>
                )}
              </div>

              <div className='space-y-2'>
                <Label htmlFor='cvv'>CVV</Label>
                <Input
                  id='cvv'
                  value={cvv}
                  onChange={e => setCvv(e.target.value)}
                  placeholder='123'
                  className={errors.cvv ? 'border-destructive' : ''}
                />
                {errors.cvv && (
                  <p className='text-sm text-destructive'>{errors.cvv}</p>
                )}
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='cardholderName'>Cardholder Name</Label>
              <Input
                id='cardholderName'
                value={cardholderName}
                onChange={e => setCardholderName(e.target.value)}
                placeholder='John Doe'
                className={errors.cardholderName ? 'border-destructive' : ''}
              />
              {errors.cardholderName && (
                <p className='text-sm text-destructive'>
                  {errors.cardholderName}
                </p>
              )}
            </div>
          </>
        )}

        {paymentType === 'ACH' && (
          <>
            <div className='space-y-2'>
              <Label htmlFor='bankName'>Bank Name</Label>
              <Input
                id='bankName'
                value={bankName}
                onChange={e => setBankName(e.target.value)}
                placeholder='Chase Bank'
                className={errors.bankName ? 'border-destructive' : ''}
              />
              {errors.bankName && (
                <p className='text-sm text-destructive'>{errors.bankName}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='routingNumber'>Routing Number</Label>
              <Input
                id='routingNumber'
                value={routingNumber}
                onChange={e => setRoutingNumber(e.target.value)}
                placeholder='*********'
                className={errors.routingNumber ? 'border-destructive' : ''}
              />
              {errors.routingNumber && (
                <p className='text-sm text-destructive'>
                  {errors.routingNumber}
                </p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='accountNumber'>Account Number</Label>
              <Input
                id='accountNumber'
                value={accountNumber}
                onChange={e => setAccountNumber(e.target.value)}
                placeholder='**********'
                className={errors.accountNumber ? 'border-destructive' : ''}
              />
              {errors.accountNumber && (
                <p className='text-sm text-destructive'>
                  {errors.accountNumber}
                </p>
              )}
            </div>
          </>
        )}

        <div className='flex items-center space-x-2 pt-2'>
          <Checkbox
            id='isDefault'
            checked={isDefault}
            onCheckedChange={checked => setIsDefault(checked as boolean)}
          />
          <Label htmlFor='isDefault' className='font-normal'>
            Set as default payment method
          </Label>
        </div>
      </div>

      <div className='flex justify-end space-x-2'>
        <Button type='button' variant='outline' onClick={onCancel}>
          Cancel
        </Button>
        <Button type='submit'>
          {isEditing ? 'Update Payment Method' : 'Add Payment Method'}
        </Button>
      </div>
    </form>
  );
}
