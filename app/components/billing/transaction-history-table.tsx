'use client';

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, Eye } from 'lucide-react';
import { Transaction, TransactionStatus } from './types';

interface TransactionHistoryTableProps {
  transactions: Transaction[];
  onViewReceipt?: (transactionId: string) => void;
  onDownloadReceipt?: (transactionId: string) => void;
  showUserInfo?: boolean;
}

export function TransactionHistoryTable({
  transactions,
  onViewReceipt,
  onDownloadReceipt,
  showUserInfo = false,
}: TransactionHistoryTableProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getStatusBadge = (status: TransactionStatus) => {
    switch (status) {
      case 'Successful':
        return <Badge className='bg-green-2010c'>Successful</Badge>;
      case 'Failed':
        return <Badge variant='destructive'>Failed</Badge>;
      case 'Pending':
        return (
          <Badge
            variant='outline'
            className='text-blue-2157c border-blue-2157c'
          >
            Pending
          </Badge>
        );
      case 'Refunded':
        return <Badge variant='secondary'>Refunded</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Description</TableHead>
            {showUserInfo && <TableHead>User</TableHead>}
            <TableHead>Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className='text-right'>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={showUserInfo ? 6 : 5}
                className='text-center py-6 text-muted-foreground'
              >
                No transactions found
              </TableCell>
            </TableRow>
          ) : (
            transactions.map(transaction => (
              <TableRow key={transaction.id}>
                <TableCell>{formatDate(transaction.date)}</TableCell>
                <TableCell>{transaction.description}</TableCell>
                {showUserInfo && (
                  <TableCell>
                    {/* In a real implementation, this would show user info */}
                    User #{transaction.userId.split('_')[1]}
                  </TableCell>
                )}
                <TableCell>{formatAmount(transaction.amount)}</TableCell>
                <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                <TableCell className='text-right'>
                  <div className='flex justify-end space-x-2'>
                    {transaction.status === 'Successful' && onViewReceipt && (
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => onViewReceipt(transaction.id)}
                        className='h-8 w-8 p-0'
                      >
                        <Eye className='h-4 w-4' />
                      </Button>
                    )}
                    {transaction.status === 'Successful' &&
                      onDownloadReceipt && (
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => onDownloadReceipt(transaction.id)}
                          className='h-8 w-8 p-0'
                        >
                          <Download className='h-4 w-4' />
                        </Button>
                      )}
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
