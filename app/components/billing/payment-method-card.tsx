'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Trash2, Edit, CheckCircle2 } from 'lucide-react';
import { PaymentMethod } from './types';

interface PaymentMethodCardProps {
  paymentMethod: PaymentMethod;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onSetDefault: (id: string) => void;
}

export function PaymentMethodCard({
  paymentMethod,
  onEdit,
  onDelete,
  onSetDefault,
}: PaymentMethodCardProps) {
  const getCardIcon = () => {
    if (paymentMethod.type === 'ACH') {
      return <CreditCard className='h-8 w-8 text-blue-2157c' />;
    }

    switch (paymentMethod.cardBrand?.toLowerCase()) {
      case 'visa':
        return <CreditCard className='h-8 w-8 text-blue-600' />;
      case 'mastercard':
        return <CreditCard className='h-8 w-8 text-red-500' />;
      case 'amex':
        return <CreditCard className='h-8 w-8 text-blue-800' />;
      default:
        return (
          <CreditCard className='h-8 w-8 text-[var(--custom-gray-medium)]' />
        );
    }
  };

  return (
    <Card className='w-full'>
      <CardContent className='p-6'>
        <div className='flex justify-between items-start'>
          <div className='flex items-start space-x-4'>
            <div className='bg-gray-100 p-2 rounded-md'>{getCardIcon()}</div>
            <div>
              <div className='flex items-baseline space-x-2'>
                <h3 className='font-medium text-base leading-none'>
                  {paymentMethod.type === 'ACH'
                    ? paymentMethod.bankName
                    : `${paymentMethod.cardBrand}`}
                </h3>
                {paymentMethod.isDefault && (
                  <Badge
                    variant='outline'
                    className='text-green-2010c border-green-2010c text-xs leading-none flex items-center'
                  >
                    Default
                  </Badge>
                )}
              </div>
              <p className='text-sm text-muted-foreground'>
                {paymentMethod.type === 'ACH'
                  ? `Account ending in ${paymentMethod.lastFour}`
                  : `Card ending in ${paymentMethod.lastFour}`}
              </p>
              {paymentMethod.expiryDate && (
                <p className='text-sm text-muted-foreground'>
                  Expires {paymentMethod.expiryDate}
                </p>
              )}
            </div>
          </div>

          <div className='flex space-x-2'>
            {!paymentMethod.isDefault && (
              <Button
                variant='outline'
                size='sm'
                onClick={() => onSetDefault(paymentMethod.id)}
                className='h-8'
              >
                <CheckCircle2 className='h-4 w-4 mr-1' />
                Set Default
              </Button>
            )}
            <Button
              variant='outline'
              size='sm'
              onClick={() => onEdit(paymentMethod.id)}
              className='h-8'
            >
              <Edit className='h-4 w-4' />
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => onDelete(paymentMethod.id)}
              className='h-8 text-destructive hover:text-destructive'
            >
              <Trash2 className='h-4 w-4' />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
