'use client';

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Save,
  AlertTriangle,
  Plus,
  Trash2,
  <PERSON><PERSON>ircle,
  ArrowRight,
  Info,
} from 'lucide-react';
import {
  Question,
  QuestionOption,
  QuestionType,
  QuestionValidationType,
  CreateQuestionNewRequest,
  UpdateQuestionNewRequest,
  QuestionFormData,
  ValidationErrors,
} from '@/types/interview-builder-new';
import {
  createQuestion,
  updateQuestion,
  validateQuestionData,
} from '@/lib/api/interview-builder-new';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@workspace/ui/tooltip';

interface QuestionFormDialogProps {
  interviewId: string;
  question: Question | null;
  existingQuestions: Question[];
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

const QUESTION_TYPES: {
  value: QuestionType;
  label: string;
  description: string;
}[] = [
  { value: 'text', label: 'Text Input', description: 'Single line text input' },
  {
    value: 'radio',
    label: 'Radio Buttons',
    description: 'Single choice from options',
  },
  {
    value: 'select',
    label: 'Dropdown',
    description: 'Single choice from dropdown',
  },
  {
    value: 'checkbox',
    label: 'Checkboxes',
    description: 'Multiple choice options',
  },
];

const VALIDATION_RULES: {
  value: QuestionValidationType;
  label: string;
  description: string;
}[] = [
  {
    value: 'number',
    label: 'Number',
    description: 'Only numeric values allowed',
  },
  { value: 'email', label: 'Email', description: 'Valid email address format' },
  { value: 'phone', label: 'Phone', description: 'Valid phone number format' },
];

export function QuestionFormDialog({
  interviewId,
  question,
  existingQuestions,
  isOpen,
  onClose,
  onSave,
}: QuestionFormDialogProps) {
  const [formData, setFormData] = useState<QuestionFormData>({
    text: '',
    type: 'text',
    options: [],
    conditionalLogic: [],
    questionMapping: '',
    questionValidation: undefined,
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  const isEditing = !!question;

  // Helper function to strip brackets from mapping value for display
  const stripMappingBrackets = (mapping: string): string => {
    if (!mapping) return '';
    return mapping.replace(/^\[|\]$/g, '');
  };

  // Helper function to add brackets to mapping value for saving
  const addMappingBrackets = (mapping: string): string => {
    if (!mapping) return '';
    const stripped = mapping.replace(/^\[|\]$/g, '');
    return `[${stripped}]`;
  };

  useEffect(() => {
    if (question) {
      const calculatedDefaultNextQuestionId = calculateDefaultNextQuestionId();
      setFormData({
        text: question.text,
        type: question.type,
        options: question.options || [],
        conditionalLogic: question.conditionalLogic || [],
        defaultNextQuestionId:
          question.defaultNextQuestionId || calculatedDefaultNextQuestionId,
        questionMapping: stripMappingBrackets(question.questionMapping || ''),
        questionValidation: question.questionValidation,
      });
    } else {
      setFormData({
        text: '',
        type: 'text',
        options: [],
        conditionalLogic: [],
        defaultNextQuestionId: undefined,
        questionMapping: '',
        questionValidation: undefined,
      });
    }
    setErrors({});
    setActiveTab('basic');
  }, [question, isOpen, existingQuestions]);

  const handleFieldChange = (field: keyof QuestionFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // Clear validation when question type changes from text to something else
      if (field === 'type' && value !== 'text' && prev.questionValidation) {
        newData.questionValidation = undefined;
      }

      return newData;
    });

    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Special handling for defaultNextQuestionId on conditional questions
    if (field === 'defaultNextQuestionId' && value && isConditionalQuestion()) {
      updateParentDefaultNextQuestionId(value);
    }
  };

  // Helper function to update parent question's defaultNextQuestionId and inherit it in child
  const updateParentDefaultNextQuestionId = async (nextQuestionId: string) => {
    if (!question) return;

    // Find the parent question (question that has this conditional question in its options)
    const parentQuestion = existingQuestions.find(q =>
      q.options?.some(option => option.nextQuestionId === question.questionId)
    );

    if (parentQuestion) {
      try {
        // Update the parent question's defaultNextQuestionId
        const updateData: UpdateQuestionNewRequest = {
          questionId: parentQuestion.questionId,
          text: parentQuestion.text,
          type: parentQuestion.type,
          options: parentQuestion.options,
          conditionalLogic: parentQuestion.conditionalLogic || [],
          defaultNextQuestionId: nextQuestionId,
          questionMapping: parentQuestion.questionMapping,
        };

        await updateQuestion(interviewId, updateData);

        // Also update the current conditional question's defaultNextQuestionId to inherit from parent
        setFormData(prev => ({
          ...prev,
          defaultNextQuestionId: nextQuestionId,
        }));

        // Refresh the questions list to reflect the change
        if (onSave) {
          onSave();
        }
      } catch (error) {
        console.error('Failed to update parent question:', error);
      }
    }
  };

  const handleAddOption = () => {
    const newOption: QuestionOption = {
      id: `opt_${Date.now()}`,
      label: '',
      value: '',
    };
    setFormData(prev => ({
      ...prev,
      options: [...prev.options, newOption],
    }));
  };

  const handleUpdateOption = (
    index: number,
    field: keyof QuestionOption,
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.map((opt, i) =>
        i === index ? { ...opt, [field]: value } : opt
      ),
    }));
  };

  const handleRemoveOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index),
    }));
  };

  // Helper function to determine if current question is conditional
  const isConditionalQuestion = (): boolean => {
    if (!question) return false;

    // Check if this question is referenced by other questions' options
    return existingQuestions.some(q =>
      q.options?.some(option => option.nextQuestionId === question.questionId)
    );
  };

  // Helper function to get parent question for conditional questions
  const getParentQuestion = (): Question | null => {
    if (!question) return null;

    return (
      existingQuestions.find(q =>
        q.options?.some(option => option.nextQuestionId === question.questionId)
      ) || null
    );
  };

  // Helper function to calculate default next question ID
  const calculateDefaultNextQuestionId = (): string | undefined => {
    if (isConditionalQuestion()) {
      // For conditional questions, inherit from parent question
      const parentQuestion = existingQuestions.find(q =>
        q.options?.some(
          option => option.nextQuestionId === question?.questionId
        )
      );
      return parentQuestion?.defaultNextQuestionId;
    } else {
      // For head questions, find the next head question in order
      const headQuestions = [...existingQuestions]
        .filter(q => q.isHeadQuestion !== false)
        .sort((a, b) => a.order - b.order);

      const currentIndex = question
        ? headQuestions.findIndex(q => q.questionId === question.questionId)
        : -1;

      if (currentIndex >= 0 && currentIndex < headQuestions.length - 1) {
        return headQuestions[currentIndex + 1].questionId;
      }
    }
    return undefined;
  };

  const handleSetBranching = (optionIndex: number, nextQuestionId: string) => {
    setFormData(prev => {
      const updatedOptions = [...prev.options];

      // Update the specific option only
      updatedOptions[optionIndex] = {
        ...updatedOptions[optionIndex],
        nextQuestionId: nextQuestionId || undefined,
      };

      return {
        ...prev,
        options: updatedOptions,
      };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationErrors = validateQuestionData(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      setSaving(true);
      setErrors({});

      // Prepare the mapping value with brackets for saving
      const mappingWithBrackets = formData.questionMapping
        ? addMappingBrackets(formData.questionMapping)
        : '';

      if (isEditing && question) {
        const updateData: UpdateQuestionNewRequest = {
          questionId: question.questionId,
          text: formData.text,
          type: formData.type,
          options: formData.options,
          conditionalLogic: formData.conditionalLogic,
          defaultNextQuestionId: formData.defaultNextQuestionId,
          questionMapping: mappingWithBrackets,
          questionValidation: formData.questionValidation,
        };
        await updateQuestion(interviewId, updateData);
      } else {
        const createData: CreateQuestionNewRequest = {
          text: formData.text,
          type: formData.type,
          options: formData.options,
          conditionalLogic: formData.conditionalLogic,
          defaultNextQuestionId: formData.defaultNextQuestionId,
          questionMapping: mappingWithBrackets,
          questionValidation: formData.questionValidation,
        };
        await createQuestion(interviewId, createData);
      }

      onSave();
    } catch (err) {
      setErrors({ submit: 'Failed to save question. Please try again.' });
      console.error('Error saving question:', err);
    } finally {
      setSaving(false);
    }
  };

  const needsOptions = ['radio', 'select', 'checkbox'].includes(formData.type);
  const supportsBranching = ['radio', 'select'].includes(formData.type);

  // Get available questions for branching (excluding current question)
  const availableQuestions = existingQuestions.filter(
    q => !question || q.questionId !== question.questionId
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center space-x-2'>
            <HelpCircle className='h-5 w-5' />
            <span>{isEditing ? 'Edit Question' : 'Create New Question'}</span>
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update question details, options, and logic.'
              : 'Create a new interview question with options and conditional logic.'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          {errors.submit && (
            <Alert variant='destructive' className='mb-4'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-4'>
              <TabsTrigger value='basic'>Basic Info</TabsTrigger>
              <TabsTrigger value='options'>Options</TabsTrigger>
              <TabsTrigger value='branching'>Branching</TabsTrigger>
              <TabsTrigger value='mapping'>Mapping</TabsTrigger>
            </TabsList>

            <TabsContent value='basic' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Question Details</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='text'>
                      Question Text <span className='text-red-500'>*</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info className='w-4 h-4 text-gray-500 cursor-help' />
                          </TooltipTrigger>
                          <TooltipContent className='max-w-md'>
                            <div className='space-y-2'>
                              <p className='font-semibold'>
                                Available Variables:
                              </p>
                              <div className='text-sm space-y-1'>
                                <p>
                                  <strong>User Information:</strong>
                                </p>
                                <p>[MEMBER_NAME], [FIRST_NAME], [LAST_NAME]</p>
                                <p>[EMAIL], [PHONE_NUMBER]</p>
                                <p>[ADDRESS], [STATE_LABEL]</p>
                                <p>[DATE_OF_BIRTH], [GENDER]</p>
                              </div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </Label>
                    <Textarea
                      id='text'
                      value={formData.text}
                      onChange={e => handleFieldChange('text', e.target.value)}
                      placeholder='What is your full legal name?'
                      rows={2}
                    />
                    {errors.text && (
                      <p className='text-sm text-red-600'>{errors.text}</p>
                    )}
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='type'>
                      Question Type <span className='text-red-500'>*</span>
                    </Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value: QuestionType) =>
                        handleFieldChange('type', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select question type' />
                      </SelectTrigger>
                      <SelectContent>
                        {QUESTION_TYPES.map(type => (
                          <SelectItem key={type.value} value={type.value}>
                            <div>
                              <div className='font-medium'>{type.label}</div>
                              <div className='text-sm text-gray-500'>
                                {type.description}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.type && (
                      <p className='text-sm text-red-600'>{errors.type}</p>
                    )}
                  </div>

                  {/* Validation Rule Selection - Only for text type questions */}
                  {formData.type === 'text' && (
                    <div className='space-y-2'>
                      <Label htmlFor='questionValidation'>
                        Validation Rule
                      </Label>
                      <Select
                        value={formData.questionValidation || '__none__'}
                        onValueChange={(value: string) =>
                          handleFieldChange(
                            'questionValidation',
                            value === '__none__'
                              ? undefined
                              : (value as QuestionValidationType)
                          )
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='Select validation rule (optional)' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='__none__'>
                            No validation
                          </SelectItem>
                          {VALIDATION_RULES.map(rule => (
                            <SelectItem key={rule.value} value={rule.value}>
                              <div>
                                <div className='font-medium'>{rule.label}</div>
                                <div className='text-sm text-gray-500'>
                                  {rule.description}
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.questionValidation && (
                        <p className='text-sm text-red-600'>
                          {errors.questionValidation}
                        </p>
                      )}
                    </div>
                  )}

                  <div className='space-y-2'>
                    <Label htmlFor='defaultNextQuestionId'>
                      Default Next Question
                    </Label>
                    <Select
                      value={formData.defaultNextQuestionId || '__none__'}
                      onValueChange={value =>
                        handleFieldChange(
                          'defaultNextQuestionId',
                          value === '__none__' ? undefined : value
                        )
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder='Select next question' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='__none__'>
                          {isConditionalQuestion()
                            ? 'Inherit from parent (will be saved)'
                            : 'End of interview'}
                        </SelectItem>
                        {availableQuestions.map(q => (
                          <SelectItem key={q.questionId} value={q.questionId}>
                            {q.text}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {isConditionalQuestion() && (
                      <p className='text-sm text-blue-600'>
                        This is a conditional question. When you select a next
                        question, both this question and its parent will be
                        updated to use the same defaultNextQuestionId value.
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='options' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Answer Options</CardTitle>
                  <CardDescription>
                    {needsOptions
                      ? 'Define the available answer choices for this question.'
                      : 'This question type does not require predefined options.'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {needsOptions ? (
                    <div className='space-y-4'>
                      {formData.options.map((option, index) => (
                        <div
                          key={option.id}
                          className='flex items-center space-x-2'
                        >
                          <div className='flex-1 grid grid-cols-2 gap-2'>
                            <Input
                              placeholder='Option label'
                              value={option.label}
                              onChange={e =>
                                handleUpdateOption(
                                  index,
                                  'label',
                                  e.target.value
                                )
                              }
                            />
                            <Input
                              placeholder='Option value'
                              value={option.value}
                              onChange={e =>
                                handleUpdateOption(
                                  index,
                                  'value',
                                  e.target.value
                                )
                              }
                            />
                          </div>
                          <Button
                            type='button'
                            variant='ghost'
                            size='sm'
                            onClick={() => handleRemoveOption(index)}
                          >
                            <Trash2 className='h-4 w-4' />
                          </Button>
                        </div>
                      ))}

                      <Button
                        type='button'
                        variant='outline'
                        onClick={handleAddOption}
                        className='w-full'
                      >
                        <Plus className='mr-2 h-4 w-4' />
                        Add Option
                      </Button>

                      {errors.options && (
                        <p className='text-sm text-red-600'>{errors.options}</p>
                      )}
                    </div>
                  ) : (
                    <div className='text-center py-8 text-gray-500'>
                      <HelpCircle className='mx-auto h-8 w-8 mb-2' />
                      <p>
                        Text input questions don't require predefined options.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='branching' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>
                    Conditional Branching
                  </CardTitle>
                  <CardDescription>
                    {supportsBranching
                      ? 'Set up conditional logic to show different questions based on answers. When you set a specific branch for one answer, other answers will automatically continue to the next main question.'
                      : 'Branching logic is only available for radio and dropdown questions.'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {supportsBranching && formData.options.length > 0 ? (
                    <div className='space-y-4'>
                      {formData.options.map((option, index) => {
                        return (
                          <div
                            key={option.id}
                            className='border rounded-lg p-4'
                          >
                            <div className='flex items-center justify-between mb-2'>
                              <div className='font-medium'>
                                {option.label || `Option ${index + 1}`}
                              </div>
                              <div className='flex items-center space-x-2'>
                                {option.nextQuestionId && (
                                  <Badge variant='secondary'>
                                    <ArrowRight className='mr-1 h-3 w-3' />
                                    Custom Branch
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <div className='space-y-2'>
                              <Label>Next Question</Label>
                              <Select
                                value={option.nextQuestionId || '__none__'}
                                onValueChange={value =>
                                  handleSetBranching(
                                    index,
                                    value === '__none__' ? '' : value
                                  )
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder='Continue to next question' />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='__none__'>
                                    Continue to next question
                                  </SelectItem>
                                  {availableQuestions.map(q => (
                                    <SelectItem
                                      key={q.questionId}
                                      value={q.questionId}
                                    >
                                      {q.text}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className='text-center py-8 text-gray-500'>
                      <ArrowRight className='mx-auto h-8 w-8 mb-2' />
                      <p>
                        {!supportsBranching
                          ? 'Branching is only available for radio and dropdown questions.'
                          : 'Add answer options first to set up branching logic.'}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value='mapping' className='space-y-4'>
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>Question Mapping</CardTitle>
                  <CardDescription>
                    Map this question to a specific identifier for data
                    processing
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className='space-y-2'>
                    <Label htmlFor='questionMapping'>Map Name</Label>
                    <Input
                      id='questionMapping'
                      value={formData.questionMapping || ''}
                      onChange={e =>
                        handleFieldChange('questionMapping', e.target.value)
                      }
                      placeholder='e.g., full_name, email_address, phone_number'
                    />
                    <p className='text-sm text-gray-500'>
                      Enter a unique identifier for this question. This will be
                      automatically formatted as [your_mapping] for use in
                      templates and data processing.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={saving}
            >
              Cancel
            </Button>
            <Button type='submit' disabled={saving}>
              {saving ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2' />
                  Saving...
                </>
              ) : (
                <>
                  <Save className='mr-2 h-4 w-4' />
                  {isEditing ? 'Update Question' : 'Create Question'}
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
