'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  ArrowRight,
  History,
  FileText,
  Plus,
  AlertCircle,
  Clock,
  CheckCircle,
} from 'lucide-react';
import Link from 'next/link';
import routes from '@/utils/routes';
import { Badge } from '@/components/ui/badge';
import { useLivingDocuments } from '@/hooks/useLivingDocuments';
import { LivingDocumentModal } from './LivingDocumentModal';

const documentTypeLabels = {
  EmergencyContacts: 'Emergency Contacts',
  PetCare: 'Pet Care',
  DigitalAssets: 'Digital Assets',
  EndOfLifeWishes: 'End of Life Wishes',
  MedicalDirectives: 'Medical Directives',
  Other: 'Other',
};

export function LivingDocumentsDashboard() {
  const { documents, loading, error, addDocument, getDocumentStatus } =
    useLivingDocuments();
  const [modalOpen, setModalOpen] = useState(false);

  const handleAddDocument = async (documentData: any) => {
    await addDocument(documentData);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'current':
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      case 'due-soon':
        return <Clock className='h-4 w-4 text-yellow-500' />;
      case 'overdue':
        return <AlertCircle className='h-4 w-4 text-red-500' />;
      default:
        return <CheckCircle className='h-4 w-4 text-green-500' />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current':
        return 'text-green-600';
      case 'due-soon':
        return 'text-yellow-600';
      case 'overdue':
        return 'text-red-600';
      default:
        return 'text-green-600';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <>
      <Card className='w-full max-w-3xl mb-6'>
        <CardHeader>
          <CardTitle>Living Documents</CardTitle>
          <CardDescription>
            Manage dynamic documents that require regular updates like emergency
            contacts, pet care instructions, and digital assets.
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          {loading ? (
            <p>Loading documents...</p>
          ) : error ? (
            <div className='text-red-500'>
              <p>Error loading documents: {error}</p>
              <p className='text-sm mt-1'>
                Please try again later or contact support.
              </p>
            </div>
          ) : documents.length === 0 ? (
            <div className='text-center py-8'>
              <FileText className='mx-auto h-12 w-12 text-[var(--custom-gray-medium)] mb-4' />
              <p className='text-[var(--custom-gray-medium)] mb-4'>
                You haven't created any living documents yet.
              </p>
              <p className='text-sm text-[var(--custom-gray-medium)] mb-6'>
                Living documents help you keep important information up-to-date
                with regular reminders.
              </p>
            </div>
          ) : (
            <div className='space-y-4'>
              {documents.slice(0, 3).map(document => {
                const status = getDocumentStatus(document);
                return (
                  <div
                    key={document.id}
                    className='flex items-center justify-between p-4 bg-gray-50 rounded-md'
                  >
                    <div className='flex-1'>
                      <div className='flex items-center gap-2 mb-1'>
                        <span className='font-medium'>{document.title}</span>
                        <Badge variant='outline' className='text-xs'>
                          {documentTypeLabels[document.documentType]}
                        </Badge>
                        {document.status === 'Draft' && (
                          <Badge
                            variant='outline'
                            className='text-xs border-gray-500'
                          >
                            Draft
                          </Badge>
                        )}
                      </div>
                      <div className='flex items-center gap-4 text-sm text-[var(--custom-gray-medium)]'>
                        <span>
                          Last updated: {formatDate(document.updatedAt)}
                        </span>
                        <span>
                          Next review: {formatDate(document.nextReviewDate)}
                        </span>
                        <div
                          className={`flex items-center gap-1 ${getStatusColor(status)}`}
                        >
                          {getStatusIcon(status)}
                          <span className='capitalize'>
                            {status.replace('-', ' ')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}

              {documents.length > 3 && (
                <p className='text-sm text-[var(--custom-gray-medium)] italic'>
                  +{documents.length - 3} more documents
                </p>
              )}
            </div>
          )}

          <div className='flex gap-2'>
            <Button
              variant='outline'
              size='sm'
              className='bg-gray-900 text-white hover:bg-gray-800 hover:text-white'
              onClick={() => setModalOpen(true)}
            >
              <Plus className='mr-2 h-4 w-4' />
              Create Document
            </Button>

            <Button
              variant='outline'
              size='sm'
              asChild
              className='bg-gray-900 text-white hover:bg-gray-800 hover:text-white'
            >
              <Link href={routes.livingDocuments} className='flex items-center'>
                <History className='mr-2 h-4 w-4' />
                View All
              </Link>
            </Button>
          </div>
        </CardContent>
        <CardFooter className='justify-end'>
          <Button variant='ghost' className='cursor-pointer' asChild>
            <Link href={routes.livingDocuments}>
              Manage Documents
              <ArrowRight className='ml-2 h-4 w-4' />
            </Link>
          </Button>
        </CardFooter>
      </Card>

      <LivingDocumentModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        onSave={handleAddDocument}
      />
    </>
  );
}
