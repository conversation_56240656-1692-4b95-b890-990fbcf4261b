'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON>,
  Settings,
  CheckCheck,
  ExternalLink,
  Loader2,
  AlertCircle,
} from 'lucide-react';
import { useNotifications } from '@/lib/notifications/notification-context';
import { useRole } from '@/lib/roles/role-context';
import { NotificationItem } from './notification-item';
import {
  NotificationFilters,
  NotificationCategory,
} from '@/types/notifications';

interface NotificationDropdownProps {
  onClose: () => void;
  className?: string;
}

export function NotificationDropdown({
  onClose,
  className = '',
}: NotificationDropdownProps) {
  const [activeFilter, setActiveFilter] = useState<
    NotificationCategory | 'all'
  >('all');
  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);

  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAllAsRead,
    fetchNotifications,
  } = useNotifications();

  const { userContext } = useRole();

  // Filter notifications based on active filter
  const filteredNotifications = notifications.filter(notification => {
    if (activeFilter === 'all') return true;
    return notification.category === activeFilter;
  });

  // Get the appropriate notifications page URL based on user role
  const getNotificationsPageUrl = () => {
    if (!userContext) return '/notifications';

    switch (userContext.role) {
      case 'administrator':
        return '/admin/notifications';
      case 'welon_trust':
      case 'linked_account':
        return '/member/notifications';
      default:
        return '/member/notifications';
    }
  };

  // Get the appropriate settings page URL based on user role
  const getSettingsPageUrl = () => {
    return '/dashboard/settings';
  };

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) return;

    setIsMarkingAllRead(true);
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all as read:', error);
    } finally {
      setIsMarkingAllRead(false);
    }
  };

  const handleFilterChange = async (filter: NotificationCategory | 'all') => {
    setActiveFilter(filter);

    // Fetch filtered notifications
    if (filter !== 'all') {
      const filters: NotificationFilters = {
        category: filter as NotificationCategory,
      };
      await fetchNotifications(filters);
    } else {
      await fetchNotifications();
    }
  };

  const filterOptions = [
    { value: 'all' as const, label: 'All', count: notifications.length },
    {
      value: NotificationCategory.URGENT,
      label: 'Urgent',
      count: notifications.filter(
        n => n.category === NotificationCategory.URGENT
      ).length,
    },
    {
      value: NotificationCategory.ACTION_REQUIRED,
      label: 'Action',
      count: notifications.filter(
        n => n.category === NotificationCategory.ACTION_REQUIRED
      ).length,
    },
    {
      value: NotificationCategory.INFORMATIONAL,
      label: 'Info',
      count: notifications.filter(
        n => n.category === NotificationCategory.INFORMATIONAL
      ).length,
    },
  ];

  return (
    <Card
      className={`w-80 max-w-[90vw] max-h-[600px] shadow-lg border ${className}`}
    >
      <CardHeader className='pb-2 px-4'>
        <div className='flex items-center justify-between mb-2'>
          <CardTitle className='flex items-center space-x-2 text-base'>
            <Bell className='h-4 w-4' />
            <span>Notifications</span>
            {unreadCount > 0 && (
              <Badge
                variant='destructive'
                className='ml-1 text-xs px-1.5 py-0.5'
              >
                {unreadCount}
              </Badge>
            )}
          </CardTitle>

          <div className='flex items-center space-x-1'>
            {/* Settings Button */}
            <Link href={getSettingsPageUrl()} onClick={onClose}>
              <Button
                variant='ghost'
                size='sm'
                className='cursor-pointer h-7 w-7 p-0'
              >
                <Settings className='h-3 w-3' />
                <span className='sr-only'>Notification settings</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Mark All as Read Button */}
        {unreadCount > 0 && (
          <Button
            variant='ghost'
            size='sm'
            onClick={handleMarkAllAsRead}
            disabled={isMarkingAllRead}
            className='text-xs cursor-pointer w-full justify-start px-2 py-1 h-auto mb-2'
          >
            {isMarkingAllRead ? (
              <Loader2 className='h-3 w-3 animate-spin mr-2' />
            ) : (
              <CheckCheck className='h-3 w-3 mr-2' />
            )}
            Mark all as read
          </Button>
        )}

        {/* Filter Tabs */}
        <div className='flex gap-1 mt-2'>
          {filterOptions.map(option => (
            <Button
              key={option.value}
              variant={activeFilter === option.value ? 'default' : 'ghost'}
              size='sm'
              onClick={() => handleFilterChange(option.value)}
              className='text-xs cursor-pointer px-2 py-1 h-6 min-w-0 flex-1 flex items-center justify-center'
            >
              <span className='truncate text-xs'>{option.label}</span>
              {option.count > 0 && (
                <span className='ml-1 text-xs opacity-70'>{option.count}</span>
              )}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className='p-0'>
        {/* Loading State */}
        {isLoading && (
          <div className='flex items-center justify-center py-8'>
            <Loader2 className='h-6 w-6 animate-spin text-[var(--custom-gray-medium)]' />
            <span className='ml-2 text-sm text-[var(--custom-gray-medium)]'>
              Loading notifications...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className='flex items-center justify-center py-8 px-4'>
            <AlertCircle className='h-5 w-5 text-red-500 mr-2' />
            <span className='text-sm text-red-600'>{error}</span>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && !error && filteredNotifications.length === 0 && (
          <div className='flex flex-col items-center justify-center py-8 px-4 text-center'>
            <Bell className='h-12 w-12 text-[var(--custom-gray-light)] mb-3' />
            <h3 className='text-sm font-medium text-[var(--custom-gray-dark)] mb-1'>
              No notifications
            </h3>
            <p className='text-xs text-[var(--custom-gray-medium)]'>
              {activeFilter === 'all'
                ? "You're all caught up!"
                : `No ${activeFilter.replace('_', ' ')} notifications`}
            </p>
          </div>
        )}

        {/* Notifications List */}
        {!isLoading && !error && filteredNotifications.length > 0 && (
          <div className='max-h-80 overflow-y-auto'>
            {filteredNotifications.slice(0, 8).map(notification => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onClose={onClose}
                compact={true}
              />
            ))}

            {/* Show More Link */}
            {filteredNotifications.length > 8 && (
              <div className='p-3 border-t border-gray-100'>
                <Link href={getNotificationsPageUrl()} onClick={onClose}>
                  <Button
                    variant='ghost'
                    className='w-full text-sm cursor-pointer h-8'
                  >
                    View all notifications
                    <ExternalLink className='h-3 w-3 ml-1' />
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}

        {/* Footer */}
        {!isLoading &&
          !error &&
          filteredNotifications.length > 0 &&
          filteredNotifications.length <= 8 && (
            <div className='p-3 border-t border-gray-100'>
              <Link href={getNotificationsPageUrl()} onClick={onClose}>
                <Button
                  variant='ghost'
                  className='w-full text-sm cursor-pointer h-8'
                >
                  View all notifications
                  <ExternalLink className='h-3 w-3 ml-1' />
                </Button>
              </Link>
            </div>
          )}
      </CardContent>
    </Card>
  );
}
