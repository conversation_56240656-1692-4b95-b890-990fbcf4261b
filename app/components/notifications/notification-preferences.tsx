'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Bell, CheckCircle, AlertCircle } from 'lucide-react';
import {
  NotificationCategory,
  DeliveryChannel,
  NotificationPreferences as NotificationPreferencesType,
} from '@/types/notifications';
import { useNotifications } from '@/lib/notifications/notification-context';

interface NotificationPreferencesProps {
  className?: string;
}

export function NotificationPreferences({
  className = '',
}: NotificationPreferencesProps) {
  const { preferences, updatePreferences, isLoading } = useNotifications();
  const [localPreferences, setLocalPreferences] = useState<
    NotificationPreferencesType['preferences'] | null
  >(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  // Initialize local preferences when preferences are loaded
  useEffect(() => {
    if (preferences) {
      setLocalPreferences(preferences.preferences);
    }
  }, [preferences]);

  const handlePreferenceChange = (
    category: NotificationCategory,
    channel: keyof NotificationPreferencesType['preferences'][NotificationCategory],
    value: boolean
  ) => {
    if (!localPreferences) return;

    setLocalPreferences(prev => {
      if (!prev) return null;

      return {
        ...prev,
        [category]: {
          ...prev[category],
          [channel]: value,
        },
      };
    });
  };

  const handleSave = async () => {
    if (!localPreferences) return;

    setIsSaving(true);
    setSaveStatus(null);

    try {
      await updatePreferences(localPreferences);
      setSaveStatus({
        type: 'success',
        message: 'Notification preferences saved successfully!',
      });
    } catch (error) {
      setSaveStatus({
        type: 'error',
        message: 'Failed to save preferences. Please try again.',
      });
    } finally {
      setIsSaving(false);
    }

    // Clear status after 5 seconds
    setTimeout(() => setSaveStatus(null), 5000);
  };

  const hasChanges = () => {
    if (!preferences || !localPreferences) return false;
    return (
      JSON.stringify(preferences.preferences) !==
      JSON.stringify(localPreferences)
    );
  };

  if (isLoading || !localPreferences) {
    return (
      <Card className={className}>
        <CardContent className='flex items-center justify-center py-8'>
          <Loader2 className='h-6 w-6 animate-spin text-[var(--custom-gray-medium)]' />
          <span className='ml-2 text-sm text-[var(--custom-gray-medium)]'>
            Loading preferences...
          </span>
        </CardContent>
      </Card>
    );
  }

  const categoryLabels = {
    [NotificationCategory.INFORMATIONAL]: {
      title: 'Informational',
      description: 'Routine updates like newsletters and policy changes',
    },
    [NotificationCategory.ACTION_REQUIRED]: {
      title: 'Action Required',
      description: 'Notifications that require your attention or action',
    },
    [NotificationCategory.URGENT]: {
      title: 'Urgent',
      description:
        'Critical alerts requiring immediate attention (cannot be disabled)',
    },
  };

  const channelLabels = {
    email: 'Email',
    sms: 'SMS',
    inApp: 'In-App',
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div>
        <h2 className='text-2xl font-bold text-[var(--custom-gray-dark)] mb-2'>
          Notification Preferences
        </h2>
        <p className='text-[var(--custom-gray-medium)]'>
          Choose how you'd like to receive different types of notifications.
        </p>
      </div>

      {/* Save Status Alert */}
      {saveStatus && (
        <Alert
          className={`${
            saveStatus.type === 'success'
              ? 'border-green-200 bg-green-50'
              : 'border-red-200 bg-red-50'
          }`}
        >
          {saveStatus.type === 'success' ? (
            <CheckCircle className='h-4 w-4 text-green-600' />
          ) : (
            <AlertCircle className='h-4 w-4 text-red-600' />
          )}
          <AlertDescription
            className={
              saveStatus.type === 'success' ? 'text-green-800' : 'text-red-800'
            }
          >
            {saveStatus.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Preferences Cards */}
      <div className='space-y-4'>
        {Object.entries(categoryLabels).map(([category, labels]) => (
          <Card key={category}>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Bell className='h-5 w-5' />
                <span>{labels.title}</span>
              </CardTitle>
              <CardDescription>{labels.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {Object.entries(channelLabels).map(([channel, label]) => {
                  const isUrgentCategory =
                    category === NotificationCategory.URGENT;
                  const isDisabled =
                    isUrgentCategory &&
                    (channel === 'email' || channel === 'inApp');
                  const isChecked =
                    localPreferences[category as NotificationCategory][
                      channel as keyof (typeof localPreferences)[NotificationCategory]
                    ];

                  return (
                    <div
                      key={channel}
                      className='flex items-center justify-between'
                    >
                      <div className='space-y-0.5'>
                        <Label
                          htmlFor={`${category}-${channel}`}
                          className={`text-base ${isDisabled ? 'text-[var(--custom-gray-medium)]' : ''}`}
                        >
                          {label}
                        </Label>
                        {isDisabled && (
                          <div className='text-sm text-[var(--custom-gray-medium)]'>
                            Required for urgent notifications
                          </div>
                        )}
                      </div>
                      <Switch
                        id={`${category}-${channel}`}
                        checked={isChecked}
                        onCheckedChange={value =>
                          handlePreferenceChange(
                            category as NotificationCategory,
                            channel as keyof NotificationPreferencesType['preferences'][NotificationCategory],
                            value
                          )
                        }
                        disabled={isDisabled}
                      />
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Save Button */}
      <div className='flex justify-end'>
        <Button
          onClick={handleSave}
          disabled={!hasChanges() || isSaving}
          className='cursor-pointer'
        >
          {isSaving ? (
            <>
              <Loader2 className='h-4 w-4 animate-spin mr-2' />
              Saving...
            </>
          ) : (
            'Save Preferences'
          )}
        </Button>
      </div>

      {/* Help Text */}
      <div className='text-sm text-[var(--custom-gray-medium)] bg-gray-50 p-4 rounded-lg'>
        <p className='font-medium mb-2'>Important Notes:</p>
        <ul className='space-y-1 list-disc list-inside'>
          <li>
            Urgent notifications are mandatory and cannot be disabled for email
            and in-app delivery.
          </li>
          <li>SMS notifications may incur charges from your mobile carrier.</li>
          <li>Changes take effect immediately after saving.</li>
          <li>You can update these preferences at any time.</li>
        </ul>
      </div>
    </div>
  );
}
