'use client';

import { useState, useEffect } from 'react';

interface Todo {
  id: string;
  content: string;
  createdAt: string;
  updatedAt: string;
}

interface ApiResponse {
  success: boolean;
  data: Todo[];
  message: string;
  error?: string;
}

export default function TodoExample() {
  const [todos, setTodos] = useState<Todo[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newTodoContent, setNewTodoContent] = useState('');

  // Fetch todos from the API
  const fetchTodos = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/todos');
      const result: ApiResponse = await response.json();
      
      if (result.success) {
        setTodos(result.data);
      } else {
        setError(result.error || 'Failed to fetch todos');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error fetching todos:', err);
    } finally {
      setLoading(false);
    }
  };

  // Create a new todo
  const createTodo = async () => {
    if (!newTodoContent.trim()) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/todos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content: newTodoContent }),
      });
      
      const result = await response.json();
      
      if (result.success) {
        setNewTodoContent('');
        // Refresh the todos list
        await fetchTodos();
      } else {
        setError(result.error || 'Failed to create todo');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error creating todo:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch todos on component mount
  useEffect(() => {
    fetchTodos();
  }, []);

  return (
    <div className="max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Todo Example</h2>
      
      {/* Create new todo */}
      <div className="mb-6">
        <div className="flex gap-2">
          <input
            type="text"
            value={newTodoContent}
            onChange={(e) => setNewTodoContent(e.target.value)}
            placeholder="Enter todo content..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading}
          />
          <button
            onClick={createTodo}
            disabled={loading || !newTodoContent.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Add
          </button>
        </div>
      </div>

      {/* Error display */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* Loading state */}
      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          <span className="ml-2">Loading...</span>
        </div>
      )}

      {/* Todos list */}
      <div>
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-semibold">Todos ({todos.length})</h3>
          <button
            onClick={fetchTodos}
            disabled={loading}
            className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 disabled:opacity-50"
          >
            Refresh
          </button>
        </div>
        
        {todos.length === 0 && !loading ? (
          <p className="text-gray-500 text-center py-4">No todos found</p>
        ) : (
          <ul className="space-y-2">
            {todos.map((todo) => (
              <li
                key={todo.id}
                className="p-3 bg-gray-50 rounded border border-gray-200"
              >
                <div className="font-medium">{todo.content}</div>
                <div className="text-xs text-gray-500 mt-1">
                  Created: {new Date(todo.createdAt).toLocaleString()}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
