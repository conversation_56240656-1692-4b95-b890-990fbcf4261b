'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  getActiveInterviews,
  getInterviewById,
  getUserInterviewProgress,
  saveUserAnswer,
  resetUserInterviewProgress,
  InterviewQuestion,
  InterviewWithVersion,
  UserInterviewProgress,
  UserAnswer,
} from '@/lib/api/interview-new-user';
import {
  validateQuestionAnswer,
  type QuestionValidationType,
} from '@/app/utils/questionValidation';

interface InterviewContextType {
  // Interview data
  interviews: InterviewWithVersion[];
  currentInterview: InterviewWithVersion | null;
  questions: InterviewQuestion[];

  // User progress
  userProgress: UserInterviewProgress | null;
  currentQuestionIndex: number;
  currentAnswer: string;

  // State
  isLoadingUserProgress: boolean;
  isLoading: boolean;
  isComplete: boolean;
  error: string | null;

  // Actions
  selectInterview: (interviewId: string) => Promise<void>;
  setCurrentAnswer: (answer: string) => void;
  saveAnswer: (questionId: string, answer: string) => Promise<void>;
  saveAnswerAndNavigate: (
    questionId: string,
    answer: string,
    nextQuestionId?: string
  ) => Promise<void>;
  goToNextQuestion: () => void;
  goToPreviousQuestion: () => void;
  goToQuestion: (index: number) => void;
  goToQuestionById: (questionId: string) => void;
  completeInterview: () => Promise<void>;
  resetInterview: () => Promise<void>;

  // Helpers
  getAnswerForQuestion: (questionId: string) => string | undefined;
  isQuestionAnswered: (questionId: string) => boolean;
  getProgress: () => number;
  getVisibleQuestions: () => InterviewQuestion[];
  shouldShowQuestion: (questionId: string) => boolean;
  changeIsCompeteStatus: (newStatus: boolean) => void;
}

const InterviewContext = createContext<InterviewContextType | undefined>(
  undefined
);

export const useInterviewNew = () => {
  const context = useContext(InterviewContext);
  if (context === undefined) {
    throw new Error(
      'useInterviewNew must be used within an InterviewNewProvider'
    );
  }
  return context;
};

interface InterviewNewProviderProps {
  children: React.ReactNode;
  defaultInterviewId?: string;
  defaultInterviewSelector?: 'first' | 'mostQuestions' | 'leastQuestions';
}

export const InterviewNewProvider: React.FC<InterviewNewProviderProps> = ({
  children,
  defaultInterviewId,
  defaultInterviewSelector = 'first',
}) => {
  // State
  const [interviews, setInterviews] = useState<InterviewWithVersion[]>([]);
  const [currentInterview, setCurrentInterview] =
    useState<InterviewWithVersion | null>(null);
  const [userProgress, setUserProgress] =
    useState<UserInterviewProgress | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentAnswer, setCurrentAnswer] = useState<string>('');
  const [questionHistory, setQuestionHistory] = useState<string[]>([]);
  // LOADING STATES
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingUserProgress, setIsLoadingUserProgress] = useState(true);
  const [isUpdatingUserProgress, setIsUpdatingUserProgress] =
    useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Derived state
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const questions = currentInterview?.latestVersion.questions || [];

  console.log('===> QUESTIONS', questions);

  // Load interviews on mount
  useEffect(() => {
    loadInterviews();
  }, []);

  // Load specific interview if defaultInterviewId is provided
  useEffect(() => {
    if (interviews.length > 0) {
      selectInterview(interviews[0].id);
    }
  }, [defaultInterviewId, interviews, defaultInterviewSelector]);

  // Load user progress when interview changes
  useEffect(() => {
    if (currentInterview) {
      loadUserProgress();
    }
  }, [currentInterview]);

  // Update current answer when question changes
  useEffect(() => {
    if (questions.length > 0 && currentQuestionIndex < questions.length) {
      const currentQuestion = questions[currentQuestionIndex];
      const savedAnswer = getAnswerForQuestion(currentQuestion.questionId);
      setCurrentAnswer(savedAnswer || '');
    }
  }, [currentQuestionIndex, questions, userProgress]);

  const loadInterviews = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const fetchedInterviews = await getActiveInterviews();
      setInterviews(fetchedInterviews);
    } catch (err) {
      setError('Failed to load interviews');
      console.error('Error loading interviews:', err);
    }
  };

  const selectInterview = async (interviewId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const interview = await getInterviewById(interviewId);
      if (!interview) {
        throw new Error('Interview not found');
      }
      setCurrentInterview(interview);

      // Find the first head question (isHeadQuestion = true) or first question by order
      const firstHeadQuestion = interview.latestVersion.questions.find(
        q => q.isHeadQuestion === true
      );
      const firstQuestion =
        firstHeadQuestion || interview.latestVersion.questions[0];

      if (firstQuestion) {
        const firstQuestionIndex = interview.latestVersion.questions.findIndex(
          q => q.questionId === firstQuestion.questionId
        );
        setCurrentQuestionIndex(
          firstQuestionIndex >= 0 ? firstQuestionIndex : 0
        );
        console.log(
          '===> Starting with question:',
          firstQuestion.text,
          'at index:',
          firstQuestionIndex
        );
      } else {
        setCurrentQuestionIndex(0);
      }

      // Reset question history when starting a new interview
      setQuestionHistory([]);
    } catch (err) {
      setError('Failed to load interview');
      console.error('Error selecting interview:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadUserProgress = async (isUpdating: boolean = false) => {
    if (!currentInterview) return;
    if (isUpdating) setIsUpdatingUserProgress(true);
    else setIsLoadingUserProgress(true);
    try {
      console.log('=== LOADING USER PROGRESS ===');
      const progress = await getUserInterviewProgress(
        currentInterview.latestVersion.id
      );
      console.log('Loaded progress:', progress);
      console.log('Is completed:', progress?.isCompleted);

      setUserProgress(progress);

      setIsComplete(progress?.isCompleted || false);

      // Set current question index based on progress
      if (progress && progress.currentQuestionId) {
        const questionIndex = questions.findIndex(
          q => q.questionId === progress.currentQuestionId
        );
        if (questionIndex >= 0) {
          setCurrentQuestionIndex(questionIndex);
        }
      }

      console.log('Progress state updated');
    } catch (err) {
      console.error('Error loading user progress:', err);
    } finally {
      setIsUpdatingUserProgress(false);
      setIsLoadingUserProgress(false);
    }
  };

  const saveAnswer = async (questionId: string, answer: string) => {
    if (!currentInterview) return;

    // Find the question to get validation rules
    const question = questions.find(q => q.questionId === questionId);
    if (question && question.questionValidation) {
      const validationResult = validateQuestionAnswer(
        answer,
        question.questionValidation as QuestionValidationType
      );

      if (!validationResult.isValid) {
        throw new Error(validationResult.errorMessage || 'Invalid input');
      }
    }

    try {
      const success = await saveUserAnswer(
        currentInterview.latestVersion.id,
        questionId,
        answer,
        false
      );

      if (success) {
        // Reload user progress to get updated state
        await loadUserProgress(true);
      } else {
        throw new Error('Failed to save answer');
      }
    } catch (err) {
      setError('Failed to save answer');
      console.error('Error saving answer:', err);
      throw err;
    }
  };

  const saveAnswerAndNavigate = async (
    questionId: string,
    answer: string,
    nextQuestionId?: string
  ) => {
    if (!currentInterview) return;

    try {
      console.log('===> === SAVE AND NAVIGATE ===');
      console.log('===> Question ID:', questionId);
      console.log('===> Answer:', answer);
      console.log('===> Next Question ID:', nextQuestionId);
      console.log('===> Current Question Index:', currentQuestionIndex);

      // Save the answer first
      await saveAnswer(questionId, answer);

      // Clear answers for questions that are no longer visible due to this answer change
      await clearInvisibleQuestionAnswers();

      // Get current question to check defaultNextQuestionId
      const currentQuestion = questions[currentQuestionIndex];

      console.log('===> NEXT QUESTION ID', nextQuestionId);
      console.log(
        '===> DEFAULT NEXT QUESTION ID',
        currentQuestion?.defaultNextQuestionId
      );

      // Then handle navigation
      if (nextQuestionId) {
        // Navigate to conditional question (from option branching)
        console.log('Navigating to conditional question:', nextQuestionId);
        const targetIndex = questions.findIndex(
          q => q.questionId === nextQuestionId
        );
        console.log('Target question index:', targetIndex);
        if (targetIndex >= 0) {
          console.log('Target question:', questions[targetIndex]);
          goToQuestionById(nextQuestionId);
        } else {
          console.error('Conditional question not found:', nextQuestionId);
          await completeInterview();
        }
      } else if (currentQuestion?.defaultNextQuestionId) {
        // Navigate to default next question
        console.log(
          'Navigating to default next question:',
          currentQuestion.defaultNextQuestionId
        );
        const targetIndex = questions.findIndex(
          q => q.questionId === currentQuestion.defaultNextQuestionId
        );
        console.log('Default target question index:', targetIndex);
        if (targetIndex >= 0) {
          console.log('Default target question:', questions[targetIndex]);
          goToQuestionById(currentQuestion.defaultNextQuestionId);
        } else {
          console.error(
            'Default next question not found:',
            currentQuestion.defaultNextQuestionId
          );
          await completeInterview();
        }
      } else {
        // No next question - this is the end of the interview
        console.log('No next question found - completing interview');
        await completeInterview();
      }
    } catch (err) {
      console.error('Error saving answer and navigating:', err);
      throw err;
    }
  };

  const goToNextQuestion = async () => {
    const currentQuestion = questions[currentQuestionIndex];

    if (!currentQuestion) {
      console.log('===> NO CURRENT QUESTION');
      await completeInterview();
      return;
    }

    console.log('===> GO TO NEXT QUESTION');
    console.log('===> Current Question:', currentQuestion.text);
    console.log(
      '===> Default Next Question ID:',
      currentQuestion.defaultNextQuestionId
    );

    // Check if there's a default next question
    if (currentQuestion.defaultNextQuestionId) {
      const targetIndex = questions.findIndex(
        q => q.questionId === currentQuestion.defaultNextQuestionId
      );

      if (targetIndex >= 0) {
        console.log(
          '===> Navigating to default next question:',
          questions[targetIndex].text
        );

        // Add current question to history before navigating
        setQuestionHistory(prev => [...prev, currentQuestion.questionId]);
        console.log('Added to history:', currentQuestion.questionId);

        setCurrentQuestionIndex(targetIndex);
      } else {
        console.error(
          'Default next question not found:',
          currentQuestion.defaultNextQuestionId
        );
        await completeInterview();
      }
    } else {
      // No default next question - this is the end of the interview
      console.log('===> NO DEFAULT NEXT QUESTION - COMPLETING INTERVIEW');
      await completeInterview();
    }
  };

  const goToPreviousQuestion = () => {
    console.log('===> GO TO PREVIOUS QUESTION');
    console.log('===> Question History:', questionHistory);

    if (questionHistory.length > 0) {
      // Get the previous question ID from history
      const previousQuestionId = questionHistory[questionHistory.length - 1];
      const prevQuestionIndex = questions.findIndex(
        q => q.questionId === previousQuestionId
      );

      if (prevQuestionIndex >= 0) {
        console.log(
          '===> Navigating to previous question:',
          questions[prevQuestionIndex].text
        );
        setCurrentQuestionIndex(prevQuestionIndex);
        // Remove the last item from history
        setQuestionHistory(prev => prev.slice(0, -1));
      }
    } else {
      console.log('===> No previous question in history');
    }
  };

  const goToQuestion = (index: number) => {
    if (index >= 0 && index < questions.length) {
      setCurrentQuestionIndex(index);
    }
  };

  const goToQuestionById = (questionId: string) => {
    console.log('=== GO TO QUESTION BY ID ===');
    console.log('Looking for question ID:', questionId);
    console.log(
      'Available questions:',
      questions.map(q => ({ id: q.questionId, text: q.text, order: q.order }))
    );

    const questionIndex = questions.findIndex(q => q.questionId === questionId);
    console.log('Found question at index:', questionIndex);

    if (questionIndex >= 0) {
      console.log('Setting current question index to:', questionIndex);
      console.log('Target question:', questions[questionIndex]);

      // Add current question to history before navigating
      const currentQuestion = questions[currentQuestionIndex];
      if (currentQuestion && currentQuestion.questionId !== questionId) {
        setQuestionHistory(prev => [...prev, currentQuestion.questionId]);
        console.log('Added to history:', currentQuestion.questionId);
      }

      setCurrentQuestionIndex(questionIndex);
    } else {
      console.error('Question not found with ID:', questionId);
    }
  };

  const completeInterview = async () => {
    if (!currentInterview) return;

    try {
      console.log('=== COMPLETING INTERVIEW ===');

      // Get the current question (should be the last visible question)
      const currentQuestion = questions[currentQuestionIndex];
      if (!currentQuestion) {
        throw new Error('No current question found');
      }

      console.log('Completing with question:', currentQuestion.questionId);
      console.log('Current answer:', currentAnswer);

      // Validate the final answer before completing
      if (currentQuestion.questionValidation) {
        const validationResult = validateQuestionAnswer(
          currentAnswer,
          currentQuestion.questionValidation as QuestionValidationType
        );

        if (!validationResult.isValid) {
          throw new Error(
            validationResult.errorMessage || 'Invalid input for final question'
          );
        }
      }

      // Save the current answer with completion flag
      const success = await saveUserAnswer(
        currentInterview.latestVersion.id,
        currentQuestion.questionId,
        currentAnswer,
        true // Mark as completed
      );

      if (success) {
        console.log('Interview marked as completed, reloading progress...');
        await loadUserProgress();
        console.log('Progress reloaded');
      } else {
        throw new Error('Failed to complete interview');
      }
    } catch (err) {
      setError('Failed to complete interview');
      console.error('Error completing interview:', err);
      throw err;
    }
  };

  const resetInterview = async () => {
    if (!currentInterview) return;

    try {
      const success = await resetUserInterviewProgress(
        currentInterview.latestVersion.id
      );

      if (success) {
        setUserProgress(null);
        setCurrentAnswer('');
        setQuestionHistory([]);

        // Find the first head question or first question by order
        const firstHeadQuestion = questions.find(
          q => q.isHeadQuestion === true
        );
        const firstQuestion = firstHeadQuestion || questions[0];

        if (firstQuestion) {
          const firstQuestionIndex = questions.findIndex(
            q => q.questionId === firstQuestion.questionId
          );
          setCurrentQuestionIndex(
            firstQuestionIndex >= 0 ? firstQuestionIndex : 0
          );
        } else {
          setCurrentQuestionIndex(0);
        }
      } else {
        throw new Error('Failed to reset interview');
      }
      setIsComplete(false);
    } catch (err) {
      setError('Failed to reset interview');
      console.error('Error resetting interview:', err);
      throw err;
    }
  };

  const getAnswerForQuestion = (questionId: string): string | undefined => {
    if (!userProgress?.answers) return undefined;

    const answer = userProgress.answers.find(a => a.questionId === questionId);
    return answer?.answer;
  };

  const isQuestionAnswered = (questionId: string): boolean => {
    return getAnswerForQuestion(questionId) !== undefined;
  };

  const getProgress = (): number => {
    const visibleQuestions = getVisibleQuestions();
    if (visibleQuestions.length === 0) return 0;

    const answeredVisibleCount = visibleQuestions.filter(q =>
      isQuestionAnswered(q.questionId)
    ).length;
    return Math.round((answeredVisibleCount / visibleQuestions.length) * 100);
  };

  // Helper to check if a question should be shown based on conditional logic
  const shouldShowQuestion = (questionId: string): boolean => {
    if (!userProgress?.answers) return true; // Show all questions if no answers yet

    // Find all questions that have this questionId as a nextQuestionId in their options
    const conditionalParents = questions.filter(q => {
      if (!q.options || !Array.isArray(q.options)) return false;

      return q.options.some(option => {
        try {
          if (typeof option === 'string') {
            const parsed = JSON.parse(option);
            return parsed.nextQuestionId === questionId;
          } else if (typeof option === 'object' && option !== null) {
            return (option as any).nextQuestionId === questionId;
          }
        } catch (e) {
          // Ignore parsing errors
        }
        return false;
      });
    });

    // If no conditional parents, always show the question
    if (conditionalParents.length === 0) return true;

    // Check if any parent question has an answer that leads to this question
    return conditionalParents.some(parentQuestion => {
      const parentAnswer = getAnswerForQuestion(parentQuestion.questionId);
      if (!parentAnswer) return false;

      // Check if the parent's answer matches an option that leads to this question
      return parentQuestion.options?.some(option => {
        try {
          let optionData: any;
          if (typeof option === 'string') {
            optionData = JSON.parse(option);
          } else {
            optionData = option as any;
          }

          return (
            optionData.value === parentAnswer &&
            optionData.nextQuestionId === questionId
          );
        } catch (e) {
          return false;
        }
      });
    });
  };

  // Get only the questions that should be visible based on conditional logic
  const getVisibleQuestions = (): InterviewQuestion[] => {
    return questions.filter(question =>
      shouldShowQuestion(question.questionId)
    );
  };

  // Clear answers for questions that are no longer visible
  const clearInvisibleQuestionAnswers = async () => {
    if (!currentInterview || !userProgress?.answers) return;

    const visibleQuestionIds = getVisibleQuestions().map(q => q.questionId);
    const answersToRemove = userProgress.answers.filter(
      answer => !visibleQuestionIds.includes(answer.questionId)
    );

    // If there are answers to remove, clear them from the database
    if (answersToRemove.length > 0) {
      console.log(
        'Clearing answers for invisible questions:',
        answersToRemove.map(a => a.questionId)
      );

      // Clear each invisible question's answer by saving empty string
      for (const answer of answersToRemove) {
        try {
          await saveUserAnswer(
            currentInterview.latestVersion.id,
            answer.questionId,
            '', // Clear the answer
            false
          );
        } catch (err) {
          console.error(
            'Error clearing answer for question:',
            answer.questionId,
            err
          );
        }
      }

      // Reload user progress to reflect the changes
      await loadUserProgress();
    }
  };

  const changeIsCompeteStatus = (newStatus: boolean) => {
    setIsComplete(newStatus);
  };

  const contextValue: InterviewContextType = {
    // Interview data
    interviews,
    currentInterview,
    questions,

    // User progress
    userProgress,
    currentQuestionIndex,
    currentAnswer,

    // State
    isLoadingUserProgress,
    isLoading,
    isComplete,
    error,

    // Actions
    selectInterview,
    setCurrentAnswer,
    saveAnswer,
    saveAnswerAndNavigate,
    goToNextQuestion,
    goToPreviousQuestion,
    goToQuestion,
    goToQuestionById,
    completeInterview,
    resetInterview,

    // Helpers
    getAnswerForQuestion,
    isQuestionAnswered,
    getProgress,
    getVisibleQuestions,
    shouldShowQuestion,
    changeIsCompeteStatus,
  };

  return (
    <InterviewContext.Provider value={contextValue}>
      {children}
    </InterviewContext.Provider>
  );
};
