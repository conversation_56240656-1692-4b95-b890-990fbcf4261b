'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useInterviewNew } from './interview-new-context';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ChevronLeft, ChevronRight, Info, AlertTriangle } from 'lucide-react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  validateQuestionAnswer,
  getValidationDescription,
  type QuestionValidationType,
} from '@/app/utils/questionValidation';
import { useAuth } from '@/context/AuthContext';
import { fetchUserAttributes } from 'aws-amplify/auth';
import { useQuery } from '@tanstack/react-query';

interface QuestionTooltipProps {
  content: string;
}

const QuestionTooltip: React.FC<QuestionTooltipProps> = ({ content }) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info className='w-4 h-4 ml-2 text-gray-500 cursor-help' />
        </TooltipTrigger>
        <TooltipContent>
          <p className='max-w-xs'>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Helper function to parse options from different formats
const parseOptions = (
  options?: string[] | any[]
): Array<{
  id: string;
  label: string;
  value: string;
  nextQuestionId?: string;
}> => {
  if (!options || !Array.isArray(options)) return [];

  return options.map((option, index) => {
    // Handle JSON string options (from the database)
    if (typeof option === 'string') {
      try {
        // Try to parse as JSON first
        const parsed = JSON.parse(option);
        if (typeof parsed === 'object' && parsed !== null) {
          return {
            id: parsed.id || `option_${index}`,
            label: parsed.label || parsed.value || String(parsed),
            value: parsed.value || parsed.label || String(parsed),
            nextQuestionId: parsed.nextQuestionId,
          };
        }
      } catch (e) {
        // If JSON parsing fails, treat as simple string
      }

      // Simple string option
      return {
        id: `option_${index}`,
        label: option,
        value: option,
      };
    } else if (typeof option === 'object' && option !== null) {
      // Already parsed object
      return {
        id: option.id || `option_${index}`,
        label: option.label || option.value || String(option),
        value: option.value || option.label || String(option),
        nextQuestionId: option.nextQuestionId,
      };
    } else {
      return {
        id: `option_${index}`,
        label: String(option),
        value: String(option),
      };
    }
  });
};

/**
 * Creates a standardized placeholder map from user attributes
 * @param userAttributes - User attributes from Cognito
 * @returns Record of placeholder keys to values
 */
const createUserAttributePlaceholders = (
  userAttributes: Record<string, any>
): Record<string, string> => {
  const fullName =
    `${userAttributes.given_name || ''} ${userAttributes.family_name || ''}`.trim() ||
    'N/A';
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return {
    // Member information - using given_name and family_name from registration
    '[Member_NAME]': fullName,
    '[MEMBER_NAME]': fullName,
    '[Member_FIRST_NAME]': userAttributes.given_name || 'N/A',
    '[Member_LAST_NAME]': userAttributes.family_name || 'N/A',
    '[FIRST_NAME]': userAttributes.given_name || 'N/A',
    '[LAST_NAME]': userAttributes.family_name || 'N/A',
    '[user_name]': userAttributes.given_name || 'N/A',
    '[USER_NAME]': userAttributes.given_name || 'N/A',

    // Contact information - using email and phone_number (formatted) from registration
    '[Member_EMAIL]': userAttributes.email || 'N/A',
    '[EMAIL]': userAttributes.email || 'N/A',
    '[Member_PHONE]': userAttributes.phone_number || 'N/A',
    '[PHONE_NUMBER]': userAttributes.phone_number || 'N/A',

    // Address/State information - address contains stateLabel from registration
    '[Member_ADDRESS]': userAttributes.address || 'N/A',
    '[ADDRESS]': userAttributes.address || 'N/A',
    '[Member_STATE]': userAttributes.address || 'N/A', // address field contains state from registration
    '[STATE_LABEL]': userAttributes.address || 'N/A',

    // Personal information - using birthdate (formatted) and gender from registration
    '[Member_DOB]': userAttributes.birthdate || 'N/A',
    '[DATE_OF_BIRTH]': userAttributes.birthdate || 'N/A',
    '[BIRTHDATE]': userAttributes.birthdate || 'N/A',
    '[Member_GENDER]': userAttributes.gender || 'N/A',
    '[GENDER]': userAttributes.gender || 'N/A',

    // Document metadata
    '[TODAY_DATE]': currentDate,
    '[CURRENT_DATE]': currentDate,
  };
};

/**
 * Creates placeholder map from user interview answers
 * @param userAnswers - Array of user interview answers
 * @param questions - Array of interview questions to get questionMapping from
 * @returns Record of placeholder keys to values
 */
const createUserAnswerPlaceholders = (
  userAnswers: any[],
  questions: any[]
): Record<string, string> => {
  const placeholders: Record<string, string> = {};

  if (!Array.isArray(userAnswers) || !Array.isArray(questions)) {
    return placeholders;
  }

  // Create a map of questionId to questionMapping for quick lookup
  const questionMappingMap = new Map<string, string>();
  questions.forEach(question => {
    if (question?.questionId && question?.questionMapping) {
      questionMappingMap.set(question.questionId, question.questionMapping);
    }
  });

  userAnswers.forEach(answerData => {
    if (answerData?.questionId && answerData?.answer) {
      const questionMapping = questionMappingMap.get(answerData.questionId);
      if (questionMapping) {
        // Remove brackets from questionMapping if they exist and add them back
        const cleanMapping = questionMapping.replace(/^\[|\]$/g, '');
        const placeholder = `[${cleanMapping}]`;
        placeholders[placeholder] = answerData.answer;
      }
    }
  });

  return placeholders;
};

/**
 * Replaces placeholders in text with actual values
 * @param text - The text with placeholders
 * @param placeholderMap - Map of placeholders to their replacement values
 * @returns Text with placeholders replaced
 */
const replacePlaceholders = (
  text: string,
  placeholderMap: Record<string, string>
): string => {
  let populatedText = text;

  Object.entries(placeholderMap).forEach(([placeholder, value]) => {
    // Create a regex that matches the placeholder (case-insensitive and global)
    const regex = new RegExp(
      placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
      'gi'
    );
    populatedText = populatedText.replace(regex, value);
  });

  return populatedText;
};

const QuestionInput: React.FC<{
  questionId: string;
  type: string;
  options?: string[] | any[];
  value: string;
  onChange: (value: string) => void;
  onOptionSelect?: (option: {
    id: string;
    label: string;
    value: string;
    nextQuestionId?: string;
  }) => void;
  placeholder?: string;
}> = ({
  questionId,
  type,
  options,
  value,
  onChange,
  onOptionSelect,
  placeholder,
}) => {
  const parsedOptions = parseOptions(options);
  switch (type) {
    case 'text':
      return (
        <Input
          id={questionId}
          value={value}
          onChange={e => onChange(e.target.value)}
          placeholder={placeholder || 'Enter your answer...'}
          className='w-full'
        />
      );

    case 'textarea':
      return (
        <Textarea
          id={questionId}
          value={value}
          onChange={e => onChange(e.target.value)}
          placeholder={placeholder || 'Enter your answer...'}
          className='w-full min-h-[100px]'
        />
      );

    case 'radio':
      const handleRadioChange = (selectedValue: string) => {
        onChange(selectedValue);
        // Find the selected option and trigger conditional logic
        const selectedOption = parsedOptions.find(
          opt => opt.value === selectedValue
        );
        if (selectedOption && onOptionSelect) {
          onOptionSelect(selectedOption);
        }
      };

      return (
        <RadioGroup value={value} onValueChange={handleRadioChange}>
          {parsedOptions.map(option => (
            <div key={option.id} className='flex items-center space-x-2'>
              <RadioGroupItem
                value={option.value}
                id={`${questionId}-${option.id}`}
              />
              <Label htmlFor={`${questionId}-${option.id}`}>
                {option.label}
              </Label>
            </div>
          ))}
        </RadioGroup>
      );

    case 'select':
      const handleSelectChange = (selectedValue: string) => {
        onChange(selectedValue);
        // Find the selected option and trigger conditional logic
        const selectedOption = parsedOptions.find(
          opt => opt.value === selectedValue
        );
        if (selectedOption && onOptionSelect) {
          onOptionSelect(selectedOption);
        }
      };

      return (
        <Select value={value} onValueChange={handleSelectChange}>
          <SelectTrigger className='w-full'>
            <SelectValue placeholder='Select an option...' />
          </SelectTrigger>
          <SelectContent>
            {parsedOptions.map(option => (
              <SelectItem key={option.id} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );

    case 'checkbox':
      const selectedValues = value ? value.split(',').filter(Boolean) : [];

      const handleCheckboxChange = (optionValue: string, checked: boolean) => {
        let newValues: string[];
        if (checked) {
          newValues = [...selectedValues, optionValue];
        } else {
          newValues = selectedValues.filter(v => v !== optionValue);
        }
        onChange(newValues.join(','));

        // Trigger conditional logic for checkbox selections
        if (checked && onOptionSelect) {
          const selectedOption = parsedOptions.find(
            opt => opt.value === optionValue
          );
          if (selectedOption) {
            onOptionSelect(selectedOption);
          }
        }
      };

      return (
        <div className='space-y-2'>
          {parsedOptions.map(option => (
            <div key={option.id} className='flex items-center space-x-2'>
              <Checkbox
                id={`${questionId}-${option.id}`}
                checked={selectedValues.includes(option.value)}
                onCheckedChange={checked =>
                  handleCheckboxChange(option.value, checked as boolean)
                }
              />
              <Label htmlFor={`${questionId}-${option.id}`}>
                {option.label}
              </Label>
            </div>
          ))}
        </div>
      );

    default:
      return (
        <div className='text-red-500'>Unsupported question type: {type}</div>
      );
  }
};

export const QuestionCard: React.FC = () => {
  const {
    questions,
    currentQuestionIndex,
    currentAnswer,
    isLoading,
    error,
    setCurrentAnswer,
    saveAnswer,
    saveAnswerAndNavigate,
    goToNextQuestion,
    goToPreviousQuestion,
    goToQuestionById,
    getAnswerForQuestion,
    getProgress,
    getVisibleQuestions,
    completeInterview,
    userProgress,
  } = useInterviewNew();

  const { user } = useAuth();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [localAnswer, setLocalAnswer] = useState<string>('');
  const [selectedOption, setSelectedOption] = useState<{
    id: string;
    label: string;
    value: string;
    nextQuestionId?: string;
  } | null>(null);
  const [validationError, setValidationError] = useState<string>('');

  // Fetch user attributes for placeholder replacement
  const { data: userAttributes, isLoading: attributesLoading } = useQuery({
    queryKey: ['user-attributes'],
    queryFn: fetchUserAttributes,
    enabled: !!user,
  });

  // Get current question
  const currentQuestion = questions[currentQuestionIndex];
  const parsedOptions = parseOptions(currentQuestion?.options);

  // Process question text with placeholder replacement
  const processedQuestionText = useMemo(() => {
    if (!currentQuestion?.text) return '';

    // Create placeholder maps
    const userAttributePlaceholders = userAttributes
      ? createUserAttributePlaceholders(userAttributes)
      : {};

    const userAnswerPlaceholders = userProgress?.answers
      ? createUserAnswerPlaceholders(userProgress.answers, questions)
      : {};

    // Combine all placeholders
    const allPlaceholders = {
      ...userAttributePlaceholders,
      ...userAnswerPlaceholders,
    };

    console.log('===> USER ANSWERS ', userAnswerPlaceholders);

    console.log('===> All placeholders:', allPlaceholders);

    // Replace placeholders in question text
    return replacePlaceholders(currentQuestion.text, allPlaceholders);
  }, [currentQuestion?.text, userAttributes, userProgress?.answers, questions]);

  // Update local answer when question changes or current answer changes
  useEffect(() => {
    setLocalAnswer(currentAnswer);
    setSelectedOption(null); // Reset selected option when question changes
    setValidationError(''); // Clear validation errors when question changes
  }, [currentAnswer, currentQuestionIndex]);

  // Handle form field changes (no database save)
  const handleChange = (value: string) => {
    setLocalAnswer(value);
    setCurrentAnswer(value);
    setSelectedOption(null); // Reset selected option for text inputs

    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError('');
    }
  };

  // Handle option selection for conditional logic
  const handleOptionSelect = (option: {
    id: string;
    label: string;
    value: string;
    nextQuestionId?: string;
  }) => {
    console.log('Option selected:', option);
    setSelectedOption(option);
    setLocalAnswer(option.value);
    setCurrentAnswer(option.value);
  };

  // Handle next button click with database save and conditional navigation
  const handleNext = async () => {
    if (!currentQuestion) return;

    // Validate the answer before saving
    const validationResult = validateQuestionAnswer(
      localAnswer,
      currentQuestion.questionValidation as QuestionValidationType
    );

    if (!validationResult.isValid) {
      setValidationError(validationResult.errorMessage || 'Invalid input');
      return;
    }

    setIsSubmitting(true);
    try {
      // Check if this is the last visible question
      // if (isLastQuestion) {
      //   console.log('=== COMPLETING INTERVIEW ===');
      //   console.log('Last question ID:', currentQuestion.questionId);
      //   console.log('Final answer:', localAnswer);
      //
      //   // Save the final answer with completion flag
      //   await saveAnswer(currentQuestion.questionId, localAnswer);
      //
      //   // Complete the interview
      //   await completeInterview();
      //
      //   console.log('Interview completed successfully');
      //   return;
      // }

      // Find the option that matches the current answer to get conditional logic
      let nextQuestionId: string | undefined;

      if (parsedOptions.length > 0) {
        const matchingOption = parsedOptions.find(
          opt => opt.value === localAnswer
        );
        nextQuestionId = matchingOption?.nextQuestionId;

        console.log('Current answer:', localAnswer);
        console.log('Matching option:', matchingOption);
        console.log('Next question ID:', nextQuestionId);
      }

      // Save current answer and navigate based on conditional logic
      await saveAnswerAndNavigate(
        currentQuestion.questionId,
        localAnswer,
        nextQuestionId
      );
    } catch (error) {
      console.error('Error saving response:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle previous button click
  const handlePrevious = () => {
    goToPreviousQuestion();
  };

  if (isLoading) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='p-6'>
          <div className='animate-pulse space-y-4'>
            <div className='h-4 bg-gray-200 rounded w-1/4'></div>
            <div className='h-8 bg-gray-200 rounded w-3/4'></div>
            <div className='h-20 bg-gray-200 rounded'></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className='max-w-2xl mx-auto border-red-200'>
        <CardContent className='p-6'>
          <div className='text-red-600 text-center'>
            <p className='font-semibold'>Error loading interview</p>
            <p className='text-sm mt-1'>{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!currentQuestion) {
    return (
      <Card className='max-w-2xl mx-auto'>
        <CardContent className='p-6'>
          <div className='text-center text-gray-500'>
            <p>No questions available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const progress = getProgress();
  const visibleQuestions = getVisibleQuestions();
  const currentVisibleIndex = visibleQuestions.findIndex(
    q => q.questionId === questions[currentQuestionIndex]?.questionId
  );
  const isFirstQuestion = currentVisibleIndex === 0;
  const isLastQuestion = currentVisibleIndex === visibleQuestions.length - 1;

  return (
    <Card className='max-w-full mx-auto shadow-lg'>
      <CardHeader className='pb-4'>
        <div className='flex justify-between items-center mb-2'>
          <span className='text-sm text-gray-500'>
            Question {currentVisibleIndex + 1} of {visibleQuestions.length}
          </span>
          <span className='text-sm text-gray-500'>{progress}% Complete</span>
        </div>

        {/* Progress bar */}
        <div className='w-full bg-gray-200 rounded-full h-2 mb-4'>
          <div
            className='bg-blue-600 h-2 rounded-full transition-all duration-300'
            style={{ width: `${progress}%` }}
          ></div>
        </div>

        <CardTitle className='text-xl font-bold text-gray-900 flex items-center'>
          {processedQuestionText}
          {/* Show tooltip if question has conditional logic */}
          {currentQuestion.conditionalLogic &&
            currentQuestion.conditionalLogic.length > 0 && (
              <QuestionTooltip content='This question may show additional questions based on your answer' />
            )}
          {/* Show tooltip if any options have nextQuestionId (branching logic) */}
          {parsedOptions.some(opt => opt.nextQuestionId) && (
            <QuestionTooltip content='Some answers will lead to different follow-up questions' />
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className='space-y-6'>
        {/* Question input */}
        <div className='space-y-2'>
          <QuestionInput
            questionId={currentQuestion.questionId}
            type={currentQuestion.type}
            options={currentQuestion.options}
            value={localAnswer}
            onChange={handleChange}
            onOptionSelect={handleOptionSelect}
            placeholder={`Enter your ${currentQuestion.type === 'text' ? 'answer' : 'selection'}...`}
          />

          {/* Validation error display */}
          {validationError && (
            <Alert variant='destructive'>
              <AlertTriangle className='h-4 w-4' />
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}
        </div>

        {/* Navigation buttons */}
        <div className='flex justify-between pt-4'>
          <Button
            variant='outline'
            onClick={handlePrevious}
            disabled={isFirstQuestion || isSubmitting}
            className='flex items-center gap-2'
          >
            <ChevronLeft className='w-4 h-4' />
            Previous
          </Button>

          <Button
            onClick={handleNext}
            disabled={isSubmitting}
            className='flex items-center gap-2'
          >
            {isSubmitting ? (
              'Saving...'
            ) : (
              <>
                Next
                <ChevronRight className='w-4 h-4' />
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
