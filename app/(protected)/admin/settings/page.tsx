'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Shield,
  Bell,
  Database,
  Mail,
  Globe,
  CheckCircle,
  History,
  Monitor,
  LogOut,
  User,
  RefreshCw,
} from 'lucide-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { fetchDevices, signOut, forgetDevice } from 'aws-amplify/auth';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { useAuth } from '@/app/context/AuthContext';
import { fetchLoginHistory } from '../../../utils/loginHistory';
import ChangePasswordDialog from '@/components/ChangePasswordDialog';
import { fetchUserByCognitoId } from '@/lib/data/users';
import type { User as UserType } from '@/types/account';

export default function AdminSettingsPage() {
  const { refreshUser, user } = useAuth();
  const queryClient = useQueryClient();

  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const [fullUserData, setFullUserData] = useState<UserType | null>(null);
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);
  const [hasAttemptedUserDataLoad, setHasAttemptedUserDataLoad] =
    useState(false);

  // Reset attempt flag when user changes
  React.useEffect(() => {
    setHasAttemptedUserDataLoad(false);
    setFullUserData(null);
  }, [user?.userId]);

  // Load full user data when user is available
  React.useEffect(() => {
    const loadUserData = async () => {
      if (user?.userId && !hasAttemptedUserDataLoad && !isLoadingUserData) {
        setIsLoadingUserData(true);
        setHasAttemptedUserDataLoad(true);
        try {
          const userData = await fetchUserByCognitoId(user.userId);
          setFullUserData(userData);
        } catch (error) {
          console.error('Error loading user data:', error);
          // Keep fullUserData as null, but don't retry automatically
        } finally {
          setIsLoadingUserData(false);
        }
      }
    };

    loadUserData();
  }, [user?.userId, hasAttemptedUserDataLoad, isLoadingUserData]);

  const [settings, setSettings] = useState({
    // System Settings
    maintenanceMode: false,
    debugMode: false,
    autoBackup: true,

    // Security Settings
    twoFactorRequired: true,
    sessionTimeout: 30,
    passwordExpiry: 90,

    // Notification Settings
    emailNotifications: true,
    smsNotifications: false,
    emergencyAlerts: true,

    // Integration Settings
    apiRateLimit: 1000,
    webhookEnabled: true,
    auditLogging: true,
  });

  // Security-related state
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [securitySettings, setSecuritySettings] = useState({
    mfaEnabled: false,
    rememberMe: true,
  });

  // Login history query
  const {
    data: loginHistory,
    isLoading: isLoadingLoginHistory,
    refetch: refetchLoginHistory,
  } = useQuery({
    queryKey: ['adminLoginHistory'],
    queryFn: () => fetchLoginHistory(user?.signInDetails?.loginId || ''),
    enabled: !!user?.signInDetails?.loginId,
  });

  const {
    data: userDevices,
    isLoading: isLoadingDevices,
    error,
  } = useQuery({
    queryKey: ['userDevices'],
    queryFn: fetchDevices,
  });

  // Device management functions using AWS Amplify Auth
  const isCurrentDevice = (device: any) => {
    // Check if this is the current device by comparing timestamps
    // Current device typically has the most recent lastAuthenticatedDate
    if (!userDevices || userDevices.length === 0) return false;
    const mostRecentDevice = userDevices.reduce((latest, current) =>
      new Date(current.lastAuthenticatedDate || 0) >
      new Date(latest.lastAuthenticatedDate || 0)
        ? current
        : latest
    );
    return device.id === mostRecentDevice.id;
  };

  const handleForgetDevice = async (deviceId: string) => {
    try {
      console.log('===> Forgetting device', deviceId);
      await forgetDevice({ device: { id: deviceId } });
      toast.success('Device removed successfully');
      queryClient.invalidateQueries({ queryKey: ['userDevices'] });
    } catch (error) {
      console.error('Error forgetting device:', error);
      toast.error('Failed to remove device');
    }
  };

  const handleSignOutAllDevices = async () => {
    try {
      setIsSigningOut(true);
      await signOut({ global: true });
      toast.success('Successfully signed out from all devices');
      await refreshUser();
      queryClient.invalidateQueries({ queryKey: ['userDevices'] });
    } catch (error) {
      console.error('Error signing out from all devices:', error);
      toast.error('Failed to sign out from all devices');
    } finally {
      setIsSigningOut(false);
    }
  };

  const handleRememberMeChange = (checked: boolean) => {
    setSecuritySettings(prev => ({
      ...prev,
      rememberMe: checked,
    }));
    toast.success(checked ? 'Remember me enabled' : 'Remember me disabled');
  };

  const handleToggleSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div>
        <h1 className='text-3xl font-bold mb-2 text-[var(--foreground)]'>
          System Settings
        </h1>
        <p className='text-lg text-[var(--custom-gray-medium)]'>
          Configure system-wide settings and preferences
        </p>
      </div>

      {/* Notification */}
      {notification && (
        <Alert
          className={`${
            notification.type === 'success'
              ? 'border-green-200 bg-green-50'
              : 'border-red-200 bg-red-50'
          }`}
        >
          {notification.type === 'success' ? (
            <CheckCircle className='h-4 w-4 text-green-600' />
          ) : (
            <Shield className='h-4 w-4 text-red-600' />
          )}
          <AlertDescription
            className={
              notification.type === 'success'
                ? 'text-green-800'
                : 'text-red-800'
            }
          >
            {notification.message}
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue='system' className='w-full'>
        <TabsList className='grid w-full grid-cols-5'>
          <TabsTrigger value='system' className='cursor-pointer flex-1'>
            <Settings className='h-4 w-4 mr-2' />
            System
          </TabsTrigger>
          <TabsTrigger value='security' className='cursor-pointer flex-1'>
            <Shield className='h-4 w-4 mr-2' />
            Security
          </TabsTrigger>
          <div className='relative group flex-1'>
            <TabsTrigger
              value='notifications'
              disabled
              className='cursor-not-allowed opacity-50 w-full flex items-center justify-center'
            >
              <Bell className='h-4 w-4 mr-2' />
              Notifications
            </TabsTrigger>
            <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50'>
              In development
            </div>
          </div>
          <div className='relative group flex-1'>
            <TabsTrigger
              value='integrations'
              disabled
              className='cursor-not-allowed opacity-50 w-full flex items-center justify-center'
            >
              <Globe className='h-4 w-4 mr-2' />
              Integrations
            </TabsTrigger>
            <div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50'>
              In development
            </div>
          </div>
          <TabsTrigger value='history' className='cursor-pointer flex-1'>
            <History className='h-4 w-4 mr-2' />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value='system' className='space-y-6'>
          {/* Admin Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <User className='h-5 w-5' />
                <span>Administrator Information</span>
              </CardTitle>
              <CardDescription>
                Current administrator account details
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='space-y-2'>
                  <Label className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    Email
                  </Label>
                  <div className='p-3 bg-[var(--background)] rounded-md'>
                    <p className='text-sm'>
                      {user?.signInDetails?.loginId || 'Not available'}
                    </p>
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    User ID
                  </Label>
                  <div className='p-3 bg-[var(--background)] rounded-md'>
                    <p className='text-sm font-mono'>
                      {user?.userId || 'Not available'}
                    </p>
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    Role
                  </Label>
                  <div className='p-3 bg-[var(--background)] rounded-md'>
                    {isLoadingUserData ? (
                      <p className='text-sm'>Loading...</p>
                    ) : fullUserData ? (
                      <div className='flex flex-wrap gap-2'>
                        <Badge
                          variant='default'
                          className='bg-[var(--eggplant)]/10 text-[var(--eggplant)] hover:bg-[var(--eggplant)]/10'
                        >
                          {fullUserData.role}
                        </Badge>
                        {fullUserData.subrole && (
                          <Badge
                            variant='secondary'
                            className='bg-gray-100 text-gray-800 hover:bg-gray-100'
                          >
                            {fullUserData.subrole}
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <p className='text-sm'>Not available</p>
                    )}
                  </div>
                </div>
                <div className='space-y-2'>
                  <Label className='text-sm font-medium text-[var(--custom-gray-dark)]'>
                    Full Name
                  </Label>
                  <div className='p-3 bg-[var(--background)] rounded-md'>
                    <p className='text-sm'>
                      {isLoadingUserData
                        ? 'Loading...'
                        : fullUserData
                          ? `${fullUserData.firstName} ${fullUserData.lastName}`
                          : 'Not available'}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Settings className='h-5 w-5' />
                <span>System Configuration</span>
              </CardTitle>
              <CardDescription>
                Core system settings and operational modes
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label className='text-base'>Maintenance Mode</Label>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Temporarily disable user access for system maintenance
                  </div>
                </div>
                <Switch
                  checked={settings.maintenanceMode}
                  onCheckedChange={() => handleToggleSetting('maintenanceMode')}
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label className='text-base'>Debug Mode</Label>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Enable detailed logging for troubleshooting
                  </div>
                </div>
                <Switch
                  checked={settings.debugMode}
                  onCheckedChange={() => handleToggleSetting('debugMode')}
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label className='text-base'>Automatic Backups</Label>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Automatically backup system data daily
                  </div>
                </div>
                <Switch
                  checked={settings.autoBackup}
                  onCheckedChange={() => handleToggleSetting('autoBackup')}
                />
              </div>
            </CardContent>
          </Card> */}
        </TabsContent>

        <TabsContent value='security'>
          <div className='space-y-6'>
            {/* Password Settings */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center space-x-2'>
                  <Shield className='h-5 w-5' />
                  <span>Password & Authentication</span>
                </CardTitle>
                <CardDescription>
                  Manage your password and two-factor authentication
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                <div className='space-y-4'>
                  <div className='space-y-2'>
                    <Label>Password Management</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Regularly update your password to keep your account
                      secure.
                    </p>
                    <ChangePasswordDialog
                      trigger={
                        <Button className='w-full'>Change Password</Button>
                      }
                    />
                  </div>
                </div>

                <div className='border-t pt-4'>
                  <div className='flex items-center justify-between'>
                    <div className='space-y-0.5'>
                      <Label>Two-Factor Authentication</Label>
                      <p className='text-sm text-[var(--custom-gray-medium)]'>
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <Switch
                      checked={securitySettings.mfaEnabled}
                      onCheckedChange={checked =>
                        setSecuritySettings(prev => ({
                          ...prev,
                          mfaEnabled: checked,
                        }))
                      }
                    />
                  </div>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Remember Me</Label>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Stay logged in for extended periods
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.rememberMe}
                    onCheckedChange={handleRememberMeChange}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Active Sessions */}
            <Card>
              <CardHeader>
                <CardTitle>Active Sessions</CardTitle>
                <CardDescription>
                  View and manage your active login sessions across different
                  devices and browsers
                </CardDescription>
              </CardHeader>
              <CardContent className='space-y-4'>
                {isLoadingDevices ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      Loading device information...
                    </p>
                  </div>
                ) : !userDevices || userDevices.length === 0 ? (
                  <div className='py-4 text-center'>
                    <p className='text-sm text-[var(--custom-gray-medium)]'>
                      No devices found.
                    </p>
                  </div>
                ) : (
                  userDevices.map((device: any, index) => (
                    <div
                      key={device.id || index}
                      className='flex items-center justify-between p-4 border rounded-lg'
                    >
                      <div className='flex items-center space-x-3'>
                        <Monitor className='h-5 w-5 text-[var(--custom-gray-medium)]' />
                        <div>
                          <div className='flex flex-col'>
                            <p className='font-medium'>
                              {device.name
                                ? `Device ${device.name.slice(0, 10)}`
                                : 'Unknown Device'}
                            </p>
                            {isCurrentDevice(device) && (
                              <span className='text-xs text-green-600 font-medium'>
                                Current Session
                              </span>
                            )}
                          </div>
                          <p className='text-sm text-[var(--custom-gray-medium)]'>
                            Created:{' '}
                            {device.createDate
                              ? format(
                                  new Date(device.createDate),
                                  'MMM d, yyyy'
                                )
                              : 'Unknown'}
                          </p>
                          <p className='text-xs text-[var(--custom-gray-medium)]'>
                            Last used:{' '}
                            {device.lastAuthenticatedDate
                              ? format(
                                  new Date(device.lastAuthenticatedDate),
                                  'MMM d, yyyy h:mm a'
                                )
                              : 'Unknown'}
                          </p>
                        </div>
                      </div>
                      <div className='flex items-center space-x-2'>
                        {isCurrentDevice(device) && (
                          <Badge variant='default'>Current</Badge>
                        )}
                        {!isCurrentDevice(device) && (
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => handleForgetDevice(device.id)}
                            disabled={isSigningOut}
                          >
                            <LogOut className='h-4 w-4 mr-1' />
                            Remove Device
                          </Button>
                        )}
                      </div>
                    </div>
                  ))
                )}

                <Button
                  variant='outline'
                  className='w-full'
                  onClick={handleSignOutAllDevices}
                  disabled={isSigningOut}
                >
                  <LogOut className='h-4 w-4 mr-2' />
                  {isSigningOut ? 'Signing out...' : 'Sign Out All Devices'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='notifications' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Bell className='h-5 w-5' />
                <span>Notification Settings</span>
              </CardTitle>
              <CardDescription>
                Configure system notifications and alerts
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label className='text-base'>Email Notifications</Label>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Send system alerts via email
                  </div>
                </div>
                <Switch
                  checked={settings.emailNotifications}
                  onCheckedChange={() =>
                    handleToggleSetting('emailNotifications')
                  }
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label className='text-base'>SMS Notifications</Label>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Send critical alerts via SMS
                  </div>
                </div>
                <Switch
                  checked={settings.smsNotifications}
                  onCheckedChange={() =>
                    handleToggleSetting('smsNotifications')
                  }
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label className='text-base'>Emergency Alerts</Label>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    High-priority emergency notifications
                  </div>
                </div>
                <Switch
                  checked={settings.emergencyAlerts}
                  onCheckedChange={() => handleToggleSetting('emergencyAlerts')}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='integrations' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center space-x-2'>
                <Globe className='h-5 w-5' />
                <span>Integration Settings</span>
              </CardTitle>
              <CardDescription>
                API and external service configurations
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-2'>
                <Label htmlFor='apiRateLimit'>
                  API Rate Limit (requests/hour)
                </Label>
                <Input
                  id='apiRateLimit'
                  type='number'
                  value={settings.apiRateLimit}
                  onChange={e =>
                    setSettings(prev => ({
                      ...prev,
                      apiRateLimit: parseInt(e.target.value) || 1000,
                    }))
                  }
                  className='w-40'
                />
                <div className='text-sm text-[var(--custom-gray-medium)]'>
                  Maximum API requests per hour per client
                </div>
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label className='text-base'>Webhook Notifications</Label>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Enable webhook endpoints for external integrations
                  </div>
                </div>
                <Switch
                  checked={settings.webhookEnabled}
                  onCheckedChange={() => handleToggleSetting('webhookEnabled')}
                />
              </div>

              <div className='flex items-center justify-between'>
                <div className='space-y-0.5'>
                  <Label className='text-base'>Audit Logging</Label>
                  <div className='text-sm text-[var(--custom-gray-medium)]'>
                    Log all administrative actions for compliance
                  </div>
                </div>
                <Switch
                  checked={settings.auditLogging}
                  onCheckedChange={() => handleToggleSetting('auditLogging')}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='history' className='space-y-6'>
          {/* Admin Login History */}
          <Card>
            <CardHeader>
              <div className='flex items-center justify-between'>
                <div>
                  <CardTitle className='flex items-center space-x-2'>
                    <History className='h-5 w-5' />
                    <span>Admin Login History</span>
                  </CardTitle>
                  <CardDescription>
                    Track administrator login attempts and sessions
                  </CardDescription>
                </div>
                <Badge variant='outline' className='text-xs'>
                  Admin Access Only
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingLoginHistory ? (
                <div className='space-y-3'>
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className='flex items-center space-x-3 p-3 border rounded-lg'
                    >
                      <div className='h-2 w-2 rounded-full bg-gray-300 animate-pulse' />
                      <div className='flex-1 space-y-2'>
                        <div className='h-4 bg-gray-300 rounded animate-pulse w-1/3' />
                        <div className='h-3 bg-gray-300 rounded animate-pulse w-1/2' />
                      </div>
                      <div className='h-3 bg-gray-300 rounded animate-pulse w-20' />
                    </div>
                  ))}
                </div>
              ) : loginHistory && loginHistory.length > 0 ? (
                <div className='space-y-3'>
                  {loginHistory.map((login, index) => (
                    <div
                      key={login.id}
                      className={`flex items-center justify-between p-3 border rounded-lg ${
                        index === 0
                          ? 'border-green-500'
                          : 'border-[var(--custom-gray-light)]'
                      }`}
                    >
                      <div className='flex items-center space-x-3'>
                        <div
                          className={`h-2 w-2 rounded-full ${
                            index === 0
                              ? 'bg-green-500'
                              : 'bg-[var(--custom-gray-medium)]'
                          }`}
                        />
                        <div>
                          <p className='font-medium'>{login.device}</p>
                          <p className='text-sm text-[var(--custom-gray-medium)]'>
                            {login.location} Device
                          </p>
                          <p className='text-xs text-[var(--foreground)] font-medium'>
                            {index === 0
                              ? 'Most recent admin login'
                              : 'Successful admin login'}
                          </p>
                        </div>
                      </div>
                      <div className='text-right'>
                        <p className='text-sm text-[var(--custom-gray-medium)]'>
                          {format(new Date(login.timestamp), 'MMM d, yyyy')}
                        </p>
                        <p className='text-xs text-[var(--custom-gray-medium)]'>
                          {format(new Date(login.timestamp), 'h:mm a')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className='text-center py-8'>
                  <Monitor className='mx-auto h-12 w-12 text-gray-400' />
                  <h3 className='mt-2 text-sm font-medium text-gray-900'>
                    No login history found
                  </h3>
                  <p className='mt-1 text-sm text-gray-500'>
                    Admin login history will appear here once available.
                  </p>
                </div>
              )}

              <div className='border-t pt-4 space-y-2'>
                <Button
                  variant='outline'
                  className='w-full'
                  onClick={() => refetchLoginHistory()}
                  disabled={isLoadingLoginHistory}
                >
                  <RefreshCw
                    className={`mr-2 h-4 w-4 ${
                      isLoadingLoginHistory ? 'animate-spin' : ''
                    }`}
                  />
                  Refresh History
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Change Password Dialog */}
      <ChangePasswordDialog
        open={showPasswordDialog}
        onOpenChange={setShowPasswordDialog}
      />
    </div>
  );
}
