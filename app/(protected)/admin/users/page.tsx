'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { UserManagementTable } from '@/components/dashboard/admin/user-management-table';
import { User } from '@/types/account';
import { useAdminAccess } from '@/lib/hooks/useAdminAccess';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function UserManagementPage() {
  const router = useRouter();
  const { canManageUsers, isAdmin, userRoles } = useAdminAccess();

  const handleEditUser = (user: User) => {
    router.push(`/admin/users/edit/${user.id}`);
  };

  const handleCreateUser = () => {
    router.push('/admin/users/create');
  };

  // Additional security check at component level
  if (!canManageUsers) {
    return (
      <div className='container mx-auto py-12 px-4'>
        <div className='flex flex-col items-center justify-center'>
          <Card className='w-full max-w-3xl'>
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground'>
                You don't have permission to manage users.
              </p>
              <p className='text-sm text-muted-foreground mt-2'>
                This feature requires full administrator privileges.
              </p>
              {userRoles.length > 0 && (
                <p className='text-sm text-muted-foreground mt-1'>
                  Your roles: {userRoles.join(', ')}
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className='h-full'>
      <UserManagementTable
        onEdit={handleEditUser}
        onCreateNew={handleCreateUser}
      />
    </div>
  );
}
