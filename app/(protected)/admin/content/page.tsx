/**
 * Admin Content Management Page
 *
 * Page for administrators to manage educational content.
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useEducationalContent } from '@/hooks/use-educational-content';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ContentType, ContentStatus } from '@/types/education';

export default function AdminContentPage() {
  const { allContent, isLoading } = useEducationalContent();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ContentStatus | 'all'>(
    'all'
  );
  const [typeFilter, setTypeFilter] = useState<ContentType | 'all'>('all');

  // Filter content based on search term and filters
  const filteredContent = allContent.filter(content => {
    // Filter by search term
    const matchesSearch =
      searchTerm === '' ||
      content.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      content.description.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by status
    const matchesStatus =
      statusFilter === 'all' || content.status === statusFilter;

    // Filter by type
    const matchesType = typeFilter === 'all' || content.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status badge
  const getStatusBadge = (status: ContentStatus) => {
    switch (status) {
      case ContentStatus.PUBLISHED:
        return <Badge className='bg-green-500'>Published</Badge>;
      case ContentStatus.DRAFT:
        return <Badge variant='outline'>Draft</Badge>;
      case ContentStatus.ARCHIVED:
        return <Badge variant='secondary'>Archived</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className='space-y-8'>
      <div className='flex flex-col space-y-2'>
        <h1 className='text-3xl font-bold'>Content Management</h1>
        <p className='text-muted-foreground'>
          Manage educational content for the platform.
        </p>
      </div>

      <div className='flex justify-between items-center'>
        <div className='flex items-center space-x-4'>
          <Input
            placeholder='Search content...'
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className='w-64'
          />
          <Select
            value={statusFilter}
            onValueChange={value =>
              setStatusFilter(value as ContentStatus | 'all')
            }
          >
            <SelectTrigger className='w-32'>
              <SelectValue placeholder='Status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Status</SelectItem>
              <SelectItem value={ContentStatus.PUBLISHED}>Published</SelectItem>
              <SelectItem value={ContentStatus.DRAFT}>Draft</SelectItem>
              <SelectItem value={ContentStatus.ARCHIVED}>Archived</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={typeFilter}
            onValueChange={value => setTypeFilter(value as ContentType | 'all')}
          >
            <SelectTrigger className='w-32'>
              <SelectValue placeholder='Type' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Types</SelectItem>
              <SelectItem value={ContentType.VIDEO}>Video</SelectItem>
              <SelectItem value={ContentType.ARTICLE}>Article</SelectItem>
              <SelectItem value={ContentType.INFOGRAPHIC}>
                Infographic
              </SelectItem>
              <SelectItem value={ContentType.AVATAR}>Avatar</SelectItem>
              <SelectItem value={ContentType.TOOLTIP}>Tooltip</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Dialog>
          <DialogTrigger asChild>
            <Button>Add New Content</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Content</DialogTitle>
              <DialogDescription>
                Create a new piece of educational content.
              </DialogDescription>
            </DialogHeader>
            <div className='grid gap-4 py-4'>
              <div className='grid grid-cols-4 items-center gap-4'>
                <label htmlFor='title' className='text-right'>
                  Title
                </label>
                <Input
                  id='title'
                  placeholder='Enter content title'
                  className='col-span-3'
                />
              </div>
              <div className='grid grid-cols-4 items-center gap-4'>
                <label htmlFor='type' className='text-right'>
                  Type
                </label>
                <Select>
                  <SelectTrigger className='col-span-3'>
                    <SelectValue placeholder='Select content type' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ContentType.VIDEO}>Video</SelectItem>
                    <SelectItem value={ContentType.ARTICLE}>Article</SelectItem>
                    <SelectItem value={ContentType.INFOGRAPHIC}>
                      Infographic
                    </SelectItem>
                    <SelectItem value={ContentType.AVATAR}>Avatar</SelectItem>
                    <SelectItem value={ContentType.TOOLTIP}>Tooltip</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className='grid grid-cols-4 items-center gap-4'>
                <label htmlFor='status' className='text-right'>
                  Status
                </label>
                <Select>
                  <SelectTrigger className='col-span-3'>
                    <SelectValue placeholder='Select status' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ContentStatus.PUBLISHED}>
                      Published
                    </SelectItem>
                    <SelectItem value={ContentStatus.DRAFT}>Draft</SelectItem>
                    <SelectItem value={ContentStatus.ARCHIVED}>
                      Archived
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button type='submit'>Create Content</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue='all'>
        <TabsList>
          <TabsTrigger value='all'>All Content</TabsTrigger>
          <TabsTrigger value='published'>Published</TabsTrigger>
          <TabsTrigger value='draft'>Drafts</TabsTrigger>
          <TabsTrigger value='archived'>Archived</TabsTrigger>
        </TabsList>

        <TabsContent value='all' className='mt-6'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredContent.map(content => (
                <TableRow key={content.id}>
                  <TableCell className='font-medium'>{content.title}</TableCell>
                  <TableCell>{content.type}</TableCell>
                  <TableCell>{getStatusBadge(content.status)}</TableCell>
                  <TableCell>{formatDate(content.updatedAt)}</TableCell>
                  <TableCell>
                    <div className='flex items-center space-x-2'>
                      <Button variant='outline' size='sm'>
                        Edit
                      </Button>
                      <Link
                        href={`/admin/content/analytics?id=${content.id}`}
                        passHref
                      >
                        <Button variant='outline' size='sm'>
                          Analytics
                        </Button>
                      </Link>
                    </div>
                  </TableCell>
                </TableRow>
              ))}

              {filteredContent.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} className='text-center py-8'>
                    No content found. Try adjusting your filters.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TabsContent>

        <TabsContent value='published' className='mt-6'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredContent
                .filter(content => content.status === ContentStatus.PUBLISHED)
                .map(content => (
                  <TableRow key={content.id}>
                    <TableCell className='font-medium'>
                      {content.title}
                    </TableCell>
                    <TableCell>{content.type}</TableCell>
                    <TableCell>{formatDate(content.updatedAt)}</TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-2'>
                        <Button variant='outline' size='sm'>
                          Edit
                        </Button>
                        <Button variant='outline' size='sm'>
                          Archive
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TabsContent>

        <TabsContent value='draft' className='mt-6'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredContent
                .filter(content => content.status === ContentStatus.DRAFT)
                .map(content => (
                  <TableRow key={content.id}>
                    <TableCell className='font-medium'>
                      {content.title}
                    </TableCell>
                    <TableCell>{content.type}</TableCell>
                    <TableCell>{formatDate(content.updatedAt)}</TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-2'>
                        <Button variant='outline' size='sm'>
                          Edit
                        </Button>
                        <Button variant='outline' size='sm'>
                          Publish
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TabsContent>

        <TabsContent value='archived' className='mt-6'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Last Updated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredContent
                .filter(content => content.status === ContentStatus.ARCHIVED)
                .map(content => (
                  <TableRow key={content.id}>
                    <TableCell className='font-medium'>
                      {content.title}
                    </TableCell>
                    <TableCell>{content.type}</TableCell>
                    <TableCell>{formatDate(content.updatedAt)}</TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-2'>
                        <Button variant='outline' size='sm'>
                          Restore
                        </Button>
                        <Button variant='destructive' size='sm'>
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TabsContent>
      </Tabs>
    </div>
  );
}
