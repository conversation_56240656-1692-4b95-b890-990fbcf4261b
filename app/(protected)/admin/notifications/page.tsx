'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Bell,
  Search,
  Filter,
  CheckCheck,
  Loader2,
  AlertCircle,
  Settings,
  BarChart3,
  Users,
  Calendar,
} from 'lucide-react';
import Link from 'next/link';
import { useNotifications } from '@/lib/notifications/notification-context';
import { NotificationItem } from '@/components/notifications/notification-item';
import {
  NotificationCategory,
  NotificationPriority,
  NotificationStatus,
  NotificationFilters,
  NotificationEventType,
} from '@/types/notifications';

export default function AdminNotificationsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState<NotificationFilters>({});
  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);

  const {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAllAsRead,
    fetchNotifications,
  } = useNotifications();

  // Filter notifications based on search and filters
  const filteredNotifications = notifications.filter(notification => {
    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower);
      if (!matchesSearch) return false;
    }

    // Category filter
    if (
      activeFilters.category &&
      notification.category !== activeFilters.category
    ) {
      return false;
    }

    // Priority filter
    if (
      activeFilters.priority &&
      notification.priority !== activeFilters.priority
    ) {
      return false;
    }

    // Status filter
    if (activeFilters.status && notification.status !== activeFilters.status) {
      return false;
    }

    // Event type filter
    if (
      activeFilters.eventType &&
      notification.eventType !== activeFilters.eventType
    ) {
      return false;
    }

    // Unread only filter
    if (
      activeFilters.unreadOnly &&
      notification.status === NotificationStatus.READ
    ) {
      return false;
    }

    return true;
  });

  const handleFilterChange = async (
    key: keyof NotificationFilters,
    value: any
  ) => {
    const newFilters = { ...activeFilters, [key]: value };
    setActiveFilters(newFilters);
    await fetchNotifications(newFilters);
  };

  const handleClearFilters = async () => {
    setActiveFilters({});
    setSearchTerm('');
    await fetchNotifications();
  };

  const handleMarkAllAsRead = async () => {
    if (unreadCount === 0) return;

    setIsMarkingAllRead(true);
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Failed to mark all as read:', error);
    } finally {
      setIsMarkingAllRead(false);
    }
  };

  // Admin-specific stats
  const quarterlyReviewNotifications = notifications.filter(
    n =>
      n.eventType === NotificationEventType.QUARTERLY_REVIEW_START ||
      n.eventType === NotificationEventType.SIGN_OFF_DEADLINE_REMINDER
  );

  const systemNotifications = notifications.filter(
    n =>
      n.eventType === NotificationEventType.POLICY_CHANGE ||
      n.eventType === NotificationEventType.EMERGENCY_ACCESS
  );

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold text-[var(--custom-gray-dark)] mb-2'>
            Admin Notifications
          </h1>
          <p className='text-lg text-[var(--custom-gray-medium)]'>
            Monitor system alerts and administrative tasks
          </p>
        </div>

        <div className='flex items-center space-x-3'>
          {unreadCount > 0 && (
            <Button
              onClick={handleMarkAllAsRead}
              disabled={isMarkingAllRead}
              variant='outline'
              className='cursor-pointer'
            >
              {isMarkingAllRead ? (
                <Loader2 className='h-4 w-4 animate-spin mr-2' />
              ) : (
                <CheckCheck className='h-4 w-4 mr-2' />
              )}
              Mark all read
            </Button>
          )}

          <Link href='/dashboard/settings'>
            <Button variant='outline' className='cursor-pointer'>
              <Settings className='h-4 w-4 mr-2' />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Admin Stats */}
      <div className='grid md:grid-cols-5 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <Bell className='h-5 w-5 text-blue-600' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                Total
              </p>
              <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                {notifications.length}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center items-start justify-between mb-2'>
              <div className='h-5 w-5 bg-red-500 rounded-full' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                Unread
              </p>
              <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                {unreadCount}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <Calendar className='h-5 w-5 text-purple-600' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                Quarterly Reviews
              </p>
              <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                {quarterlyReviewNotifications.length}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center items-start justify-between mb-2'>
              <BarChart3 className='h-5 w-5 text-green-600' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                System Alerts
              </p>
              <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                {systemNotifications.length}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center items-start justify-between mb-2'>
              <div className='h-5 w-5 bg-red-600 rounded-full' />
            </div>
            <div>
              <p className='text-sm font-medium text-[var(--custom-gray-medium)] mb-1'>
                Urgent
              </p>
              <p className='text-2xl font-bold text-[var(--custom-gray-dark)]'>
                {
                  notifications.filter(
                    n => n.category === NotificationCategory.URGENT
                  ).length
                }
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <Filter className='h-5 w-5' />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-5 gap-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--custom-gray-medium)]' />
              <Input
                placeholder='Search notifications...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Category Filter */}
            <Select
              value={activeFilters.category || ''}
              onValueChange={value =>
                handleFilterChange('category', value || undefined)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder='All categories' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=''>All categories</SelectItem>
                <SelectItem value={NotificationCategory.URGENT}>
                  Urgent
                </SelectItem>
                <SelectItem value={NotificationCategory.ACTION_REQUIRED}>
                  Action Required
                </SelectItem>
                <SelectItem value={NotificationCategory.INFORMATIONAL}>
                  Informational
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Event Type Filter */}
            <Select
              value={activeFilters.eventType || ''}
              onValueChange={value =>
                handleFilterChange('eventType', value || undefined)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder='All event types' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=''>All event types</SelectItem>
                <SelectItem
                  value={NotificationEventType.QUARTERLY_REVIEW_START}
                >
                  Quarterly Review
                </SelectItem>
                <SelectItem
                  value={NotificationEventType.SIGN_OFF_DEADLINE_REMINDER}
                >
                  Sign-off Reminder
                </SelectItem>
                <SelectItem value={NotificationEventType.EMERGENCY_ACCESS}>
                  Emergency Access
                </SelectItem>
                <SelectItem value={NotificationEventType.POLICY_CHANGE}>
                  Policy Change
                </SelectItem>
              </SelectContent>
            </Select>

            {/* Priority Filter */}
            <Select
              value={activeFilters.priority || ''}
              onValueChange={value =>
                handleFilterChange('priority', value || undefined)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder='All priorities' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=''>All priorities</SelectItem>
                <SelectItem value={NotificationPriority.HIGH}>High</SelectItem>
                <SelectItem value={NotificationPriority.MEDIUM}>
                  Medium
                </SelectItem>
                <SelectItem value={NotificationPriority.LOW}>Low</SelectItem>
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select
              value={activeFilters.status || ''}
              onValueChange={value =>
                handleFilterChange('status', value || undefined)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder='All statuses' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=''>All statuses</SelectItem>
                <SelectItem value={NotificationStatus.READ}>Read</SelectItem>
                <SelectItem value={NotificationStatus.DELIVERED}>
                  Unread
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Active Filters */}
          {(Object.keys(activeFilters).length > 0 || searchTerm) && (
            <div className='flex items-center space-x-2 mt-4 flex-wrap'>
              <span className='text-sm text-[var(--custom-gray-medium)]'>
                Active filters:
              </span>
              {searchTerm && (
                <Badge variant='secondary'>Search: {searchTerm}</Badge>
              )}
              {activeFilters.category && (
                <Badge variant='secondary'>
                  Category: {activeFilters.category}
                </Badge>
              )}
              {activeFilters.eventType && (
                <Badge variant='secondary'>
                  Event: {activeFilters.eventType.replace('_', ' ')}
                </Badge>
              )}
              {activeFilters.priority && (
                <Badge variant='secondary'>
                  Priority: {activeFilters.priority}
                </Badge>
              )}
              {activeFilters.status && (
                <Badge variant='secondary'>
                  Status: {activeFilters.status}
                </Badge>
              )}
              <Button
                variant='ghost'
                size='sm'
                onClick={handleClearFilters}
                className='cursor-pointer'
              >
                Clear all
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Notifications List */}
      <Card>
        <CardHeader>
          <CardTitle>Notifications ({filteredNotifications.length})</CardTitle>
        </CardHeader>
        <CardContent className='p-0'>
          {/* Loading State */}
          {isLoading && (
            <div className='flex items-center justify-center py-8'>
              <Loader2 className='h-6 w-6 animate-spin text-[var(--custom-gray-medium)]' />
              <span className='ml-2 text-sm text-[var(--custom-gray-medium)]'>
                Loading notifications...
              </span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className='flex items-center justify-center py-8 px-4'>
              <AlertCircle className='h-5 w-5 text-red-500 mr-2' />
              <span className='text-sm text-red-600'>{error}</span>
            </div>
          )}

          {/* Empty State */}
          {!isLoading && !error && filteredNotifications.length === 0 && (
            <div className='flex flex-col items-center justify-center py-12 px-4 text-center'>
              <Bell className='h-16 w-16 text-[var(--custom-gray-light)] mb-4' />
              <h3 className='text-lg font-medium text-[var(--custom-gray-dark)] mb-2'>
                No notifications found
              </h3>
              <p className='text-[var(--custom-gray-medium)] mb-4'>
                {notifications.length === 0
                  ? 'No administrative notifications at this time.'
                  : 'No notifications match your current filters.'}
              </p>
              {Object.keys(activeFilters).length > 0 || searchTerm ? (
                <Button
                  variant='outline'
                  onClick={handleClearFilters}
                  className='cursor-pointer'
                >
                  Clear filters
                </Button>
              ) : null}
            </div>
          )}

          {/* Notifications */}
          {!isLoading && !error && filteredNotifications.length > 0 && (
            <div>
              {filteredNotifications.map(notification => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  showActions={true}
                  compact={false}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
