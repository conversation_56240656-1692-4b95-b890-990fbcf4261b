import { NextRequest, NextResponse } from 'next/server';
import { generateClient } from 'aws-amplify/data';
import type { Schema } from '@/amplify/data/resource';
import { Amplify } from 'aws-amplify';
import outputs from '@/amplify_outputs.json';

// Configure Amplify for server-side usage
Amplify.configure(outputs, { ssr: true });

// Generate the client
const client = generateClient<Schema>();

export async function GET() {
  try {
    // Fetch todos using the client
    const { data: todos, errors } = await client.models.Todo.list();

    if (errors) {
      console.error('Errors fetching todos:', errors);
      throw new Error('Failed to fetch todos');
    }

    return NextResponse.json({
      success: true,
      data: todos,
      message: 'Todos fetched successfully'
    });

  } catch (error) {
    console.error('Error in todos API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch todos',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { content } = body;

    if (!content) {
      return NextResponse.json(
        {
          success: false,
          error: 'Content is required'
        },
        { status: 400 }
      );
    }

    // Create a new todo
    const { data: newTodo, errors } = await client.models.Todo.create({
      content
    });

    if (errors) {
      console.error('Errors creating todo:', errors);
      throw new Error('Failed to create todo');
    }

    return NextResponse.json({
      success: true,
      data: newTodo,
      message: 'Todo created successfully'
    });

  } catch (error) {
    console.error('Error creating todo:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create todo',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
