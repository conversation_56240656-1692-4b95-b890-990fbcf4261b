version: 1
backend:
  phases:
    preBuild:
      commands:
        - npm install -g pnpm
    build:
      commands:
        - echo “CFLegacy_Dev_UserPoolID=$CFLegacy_Dev_UserPoolID” >> .env
        - echo “CFLegacy_Dev_IdentityPoolID =$CFLegacy_Dev_IdentityPoolID” >> .env
        - echo “CFLegacy_Dev_authRoleARN =$CFLegacy_Dev_authRoleARN” >> .env
        - echo “CFLegacy_Dev_unauthRoleARN=$CFLegacy_Dev_unauthRoleARN” >> .env
        - echo “CFLegacy_Dev_userPoolClientID=$CFLegacy_Dev_userPoolClientID” >> .env
        - pnpm install --frozen-lockfile
        - npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID
frontend:
  phases:
    preBuild:
      commands:
        - npm install -g pnpm
    build:
      commands:
        - pnpm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - .next/cache/**/*
      - .pnpm-store/**/*