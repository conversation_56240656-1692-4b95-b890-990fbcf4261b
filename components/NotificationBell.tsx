import { useState } from 'react';
import { Bell } from 'lucide-react';
import { useNotifications } from '@/hooks/useNotifications';

export function NotificationBell() {
  const [isOpen, setIsOpen] = useState(false);
  const { notifications, unreadCount, markAsRead, refetch } =
    useNotifications();

  return (
    <div className='relative'>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className='relative p-2 text-gray-600 hover:text-gray-900'
      >
        <Bell className='w-6 h-6' />
        {unreadCount > 0 && (
          <span className='absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center'>
            {unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className='absolute right-0 mt-2 w-80 bg-white border rounded-lg shadow-lg z-50'>
          <div className='p-4 border-b flex justify-between items-center'>
            <h3 className='font-semibold'>Notifications</h3>
            <button
              onClick={refetch}
              className='text-sm text-blue-600 hover:text-blue-800'
            >
              Refresh
            </button>
          </div>
          <div className='max-h-96 overflow-y-auto'>
            {notifications.length === 0 ? (
              <div className='p-4 text-gray-500 text-center'>
                No notifications
              </div>
            ) : (
              notifications.map(notification => (
                <div
                  key={notification.id}
                  className={`p-4 border-b hover:bg-gray-50 ${
                    !notification.isRead ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => markAsRead(notification.id)}
                >
                  <p className='text-sm'>{notification.message}</p>
                  <p className='text-xs text-gray-500 mt-1'>
                    {new Date(notification.createdAt).toLocaleString()}
                  </p>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
