'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  FileText,
  AlertTriangle,
  CreditCard,
  Users,
  Shield,
  Calendar,
  UserPlus,
  Gavel,
  MoreHorizontal,
  Trash2,
  ExternalLink,
  Check,
  FileEdit,
  Clock,
  Info,
  Zap,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// Note: Using simple button approach instead of dropdown menu for now
import {
  Notification,
  NotificationEventType,
  NotificationPriority,
  NotificationCategory,
} from '@/types/notifications';
import { useNotifications } from '@/lib/notifications/notification-context';

// Utility function to format exact date and time
const formatExactDateTime = (date: Date): string => {
  const dateOptions: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  };

  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  };

  const datePart = date.toLocaleDateString('en-US', dateOptions);
  const timePart = date.toLocaleTimeString('en-US', timeOptions);

  return `${datePart} · ${timePart}`;
};

// Utility function to format time distance
const formatDistanceToNow = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths === 1 ? '' : 's'} ago`;
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} year${diffInYears === 1 ? '' : 's'} ago`;
};

// Combined timestamp formatter
const formatTimestamp = (date: Date): { exact: string; relative: string } => {
  return {
    exact: formatExactDateTime(date),
    relative: formatDistanceToNow(date),
  };
};

// Helper function to generate contextual tooltips for action buttons
const getActionButtonTooltip = (
  actionText: string,
  eventType: NotificationEventType
): string => {
  // Default tooltip
  let tooltip = `Opens ${actionText.toLowerCase()} in a new tab`;

  // Specific tooltips based on event type and action text
  switch (eventType) {
    case NotificationEventType.DOCUMENT_UPDATE:
      if (actionText.toLowerCase().includes('will')) {
        tooltip = 'Opens the updated Will document in a new tab';
      } else if (actionText.toLowerCase().includes('trust')) {
        tooltip = 'Opens the updated Trust document in a new tab';
      } else if (actionText.toLowerCase().includes('review')) {
        tooltip = 'Opens the document for review in a new tab';
      } else {
        tooltip = 'Opens the updated document in a new tab';
      }
      break;

    case NotificationEventType.DOCUMENT_STATUS_CHANGE:
      tooltip = 'Opens the document to view status changes in a new tab';
      break;

    case NotificationEventType.ATTORNEY_REVIEW_REQUEST:
      tooltip = 'Opens the document for attorney review in a new tab';
      break;

    case NotificationEventType.SIGN_OFF_DEADLINE_REMINDER:
      tooltip = 'Opens the document that requires your signature in a new tab';
      break;

    case NotificationEventType.QUARTERLY_REVIEW_START:
      tooltip = 'Opens the quarterly review dashboard in a new tab';
      break;

    case NotificationEventType.EMERGENCY_ACCESS:
      tooltip = 'Opens emergency access details in a new tab';
      break;

    case NotificationEventType.MISSED_PAYMENT:
      tooltip = 'Opens payment details and options in a new tab';
      break;

    case NotificationEventType.LINKED_ACCOUNT_INVITATION:
      tooltip = 'Opens account linking invitation in a new tab';
      break;

    default:
      // Keep the default tooltip
      break;
  }

  return tooltip;
};

interface NotificationItemProps {
  notification: Notification;
  onClose?: () => void;
  showActions?: boolean;
  compact?: boolean;
}

// Icon mapping for different notification types with enhanced specificity
const getNotificationIcon = (eventType: NotificationEventType) => {
  switch (eventType) {
    case NotificationEventType.DOCUMENT_UPDATE:
      return FileEdit; // More specific for document updates
    case NotificationEventType.DOCUMENT_STATUS_CHANGE:
      return FileText; // General document icon
    case NotificationEventType.EMERGENCY_ACCESS:
      return AlertTriangle; // Emergency/warning
    case NotificationEventType.MISSED_PAYMENT:
      return CreditCard; // Payment related
    case NotificationEventType.ROUTINE_CHECK_IN:
      return Calendar; // Scheduled events
    case NotificationEventType.QUARTERLY_REVIEW_START:
      return Shield; // Review/security
    case NotificationEventType.SIGN_OFF_DEADLINE_REMINDER:
      return Clock; // Time-sensitive
    case NotificationEventType.LINKED_ACCOUNT_INVITATION:
      return UserPlus; // User invitation
    case NotificationEventType.ATTORNEY_REVIEW_REQUEST:
      return Gavel; // Legal/attorney
    case NotificationEventType.WELON_TRUST_ASSIGNMENT:
      return Users; // Trust/group
    case NotificationEventType.POLICY_CHANGE:
      return Info; // Informational
    default:
      return FileText; // Default fallback
  }
};

// Priority color mapping
const getPriorityColor = (priority: NotificationPriority) => {
  switch (priority) {
    case NotificationPriority.HIGH:
      return 'text-red-600';
    case NotificationPriority.MEDIUM:
      return 'text-yellow-600';
    case NotificationPriority.LOW:
      return 'text-blue-600';
    default:
      return 'text-[var(--custom-gray-medium)]';
  }
};

// Category badge variant mapping
const getCategoryVariant = (category: NotificationCategory) => {
  switch (category) {
    case NotificationCategory.URGENT:
      return 'destructive' as const;
    case NotificationCategory.ACTION_REQUIRED:
      return 'default' as const;
    case NotificationCategory.INFORMATIONAL:
      return 'secondary' as const;
    default:
      return 'outline' as const;
  }
};

// Helper function to get event type badge with color and icon
const getEventTypeBadge = (
  eventType: NotificationEventType
): {
  label: string;
  variant: 'default' | 'secondary' | 'destructive' | 'outline';
  icon: string;
} => {
  switch (eventType) {
    case NotificationEventType.DOCUMENT_UPDATE:
      return { label: 'Document Updated', variant: 'default', icon: '📝' };
    case NotificationEventType.DOCUMENT_STATUS_CHANGE:
      return { label: 'Status Changed', variant: 'secondary', icon: '📄' };
    case NotificationEventType.EMERGENCY_ACCESS:
      return { label: 'Emergency Access', variant: 'destructive', icon: '🚨' };
    case NotificationEventType.MISSED_PAYMENT:
      return { label: 'Payment Issue', variant: 'destructive', icon: '💳' };
    case NotificationEventType.ROUTINE_CHECK_IN:
      return { label: 'Check-in Required', variant: 'default', icon: '📅' };
    case NotificationEventType.QUARTERLY_REVIEW_START:
      return { label: 'Quarterly Review', variant: 'default', icon: '🔍' };
    case NotificationEventType.SIGN_OFF_DEADLINE_REMINDER:
      return { label: 'Deadline Reminder', variant: 'destructive', icon: '⏰' };
    case NotificationEventType.LINKED_ACCOUNT_INVITATION:
      return { label: 'Account Invitation', variant: 'default', icon: '👥' };
    case NotificationEventType.ATTORNEY_REVIEW_REQUEST:
      return { label: 'Attorney Review', variant: 'default', icon: '⚖️' };
    case NotificationEventType.WELON_TRUST_ASSIGNMENT:
      return { label: 'Trust Assignment', variant: 'secondary', icon: '🏛️' };
    case NotificationEventType.POLICY_CHANGE:
      return { label: 'Policy Update', variant: 'secondary', icon: 'ℹ️' };
    default:
      return { label: 'Notification', variant: 'outline', icon: '📋' };
  }
};

export function NotificationItem({
  notification,
  onClose,
  showActions = true,
  compact = false,
}: NotificationItemProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { markAsRead, deleteNotification } = useNotifications();

  const Icon = getNotificationIcon(notification.eventType);
  const isUnread = notification.status !== 'read';
  const timestamp = formatTimestamp(new Date(notification.createdAt));
  const eventTypeBadge = getEventTypeBadge(notification.eventType);

  const handleMarkAsRead = async () => {
    if (isUnread) {
      try {
        await markAsRead(notification.id);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await deleteNotification(notification.id);
    } catch (error) {
      console.error('Failed to delete notification:', error);
      setIsDeleting(false);
    }
  };

  const handleActionClick = () => {
    handleMarkAsRead();
    if (onClose) {
      onClose();
    }
  };

  return (
    <div
      className={`
        relative border-b border-gray-100 hover:bg-gray-50 transition-colors
        ${isUnread ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''}
        ${compact ? 'p-2' : 'p-4'}
        ${isDeleting ? 'opacity-50 pointer-events-none' : ''}
      `}
    >
      <div
        className={`flex items-start ${compact ? 'space-x-2' : 'space-x-3'}`}
      >
        {/* Icon */}
        <div
          className={`
          flex-shrink-0 rounded-full
          ${compact ? 'p-1.5' : 'p-2'}
          ${isUnread ? 'bg-blue-100' : 'bg-gray-100'}
        `}
        >
          <Icon
            className={`${compact ? 'h-3 w-3' : 'h-4 w-4'} ${getPriorityColor(notification.priority)}`}
          />
        </div>

        {/* Content */}
        <div className='flex-1 min-w-0'>
          <div className='flex items-start justify-between'>
            <div className='flex-1'>
              {/* Title and Type Badge */}
              <div
                className={`flex items-start space-x-2 ${compact ? 'mb-1' : 'mb-2'}`}
              >
                <h4
                  className={`
                  ${compact ? 'text-xs' : 'text-sm'} font-medium text-[var(--custom-gray-dark)] truncate flex-1
                  ${isUnread ? 'font-semibold' : ''}
                `}
                >
                  {notification.title}
                </h4>
                <div className='flex items-center space-x-1 flex-shrink-0'>
                  {/* Event Type Badge with Icon */}
                  <Badge
                    variant={eventTypeBadge.variant}
                    className={`${compact ? 'text-xs px-1 py-0 h-4' : 'text-xs'} self-start flex items-center gap-1`}
                  >
                    <span className='text-xs'>{eventTypeBadge.icon}</span>
                    {!compact && eventTypeBadge.label}
                  </Badge>
                  {/* Priority Badge for urgent items */}
                  {notification.priority === NotificationPriority.HIGH && (
                    <Badge
                      variant='destructive'
                      className='text-xs px-1 py-0 h-4'
                    >
                      !
                    </Badge>
                  )}
                </div>
              </div>

              {/* Message */}
              <p
                className={`
                ${compact ? 'text-xs' : 'text-sm'} text-[var(--custom-gray-medium)] ${compact ? 'mb-1' : 'mb-2'}
                ${compact ? 'line-clamp-2' : 'line-clamp-3'}
              `}
              >
                {notification.message}
              </p>

              {/* Action Button */}
              {notification.actionUrl &&
                notification.actionText &&
                !compact && (
                  <Link
                    href={notification.actionUrl}
                    onClick={handleActionClick}
                  >
                    <Button
                      variant='outline'
                      size='sm'
                      className='mb-2 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 transition-colors cursor-pointer'
                      title={getActionButtonTooltip(
                        notification.actionText,
                        notification.eventType
                      )}
                    >
                      {notification.actionText}
                      <ExternalLink className='h-3 w-3 ml-1' />
                    </Button>
                  </Link>
                )}

              {/* Enhanced Timestamp */}
              <div
                className={`${compact ? 'text-xs' : 'text-xs'} text-[var(--custom-gray-medium)] space-y-1`}
              >
                <div className='flex items-center space-x-2'>
                  <span className='font-medium'>{timestamp.exact}</span>
                  {!compact && (
                    <span className='text-[var(--custom-gray-medium)]'>
                      ({timestamp.relative})
                    </span>
                  )}
                  {notification.readAt && (
                    <span className='text-green-600 flex items-center'>
                      <Check className='h-3 w-3 mr-1' />
                      Read
                    </span>
                  )}
                </div>
                {compact && (
                  <div className='text-[var(--custom-gray-medium)]'>
                    {timestamp.relative}
                  </div>
                )}
              </div>
            </div>

            {/* Actions Buttons */}
            {showActions && (
              <div
                className={`flex-shrink-0 ml-2 flex space-x-1 ${compact ? 'mt-1' : ''}`}
              >
                {isUnread && (
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={handleMarkAsRead}
                    className={`${compact ? 'h-6 w-6' : 'h-8 w-8'} p-0 hover:bg-green-100 hover:text-green-700 transition-colors cursor-pointer`}
                    title='Mark this notification as read'
                  >
                    <Check
                      className={`${compact ? 'h-2.5 w-2.5' : 'h-3 w-3'}`}
                    />
                    <span className='sr-only'>Mark as read</span>
                  </Button>
                )}
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className={`${compact ? 'h-6 w-6' : 'h-8 w-8'} p-0 hover:bg-red-100 hover:text-red-700 text-red-600 transition-colors cursor-pointer disabled:opacity-50`}
                  title='Delete this notification permanently'
                >
                  <Trash2
                    className={`${compact ? 'h-2.5 w-2.5' : 'h-3 w-3'}`}
                  />
                  <span className='sr-only'>Delete notification</span>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Unread indicator dot */}
      {isUnread && (
        <div className='absolute top-4 right-4 h-2 w-2 bg-blue-500 rounded-full' />
      )}
    </div>
  );
}
